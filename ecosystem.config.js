module.exports = {
  apps: [
    {
      name: 'sillytavern',
      script: 'server.js',
      args: '--autorun false',
      cwd: './SillyTavern',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production'
      },
      watch: false,
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '1G',
      error_file: '../logs/sillytavern-error.log',
      out_file: '../logs/sillytavern-out.log',
      log_file: '../logs/sillytavern-combined.log',
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      restart_delay: 4000,
      // 静默模式设置
      silent: true,
      // 禁用 PM2+ 通知
      pmx: false,
      // Windows 隐藏窗口
      windowsHide: true,
      ignore_watch: [
        'node_modules',
        'logs',
        'data',
        'backups'
      ]
    }
  ]
};
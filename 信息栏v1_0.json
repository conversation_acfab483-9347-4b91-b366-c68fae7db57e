{"id": "bd48923a-5d5f-4a4a-812c-3c537f96da7f", "name": "信息栏v1.0", "content": "// ==UserScript==\n// @name         高级信息栏设置助手 Ultra Pro Max v2.3.69 (DeepFixes, MultiNPCImprove, SummaryLogicRefine)\n// @namespace    SillyTavern.GlobalScripts\n// @match        */*\n// @version      2.3.69\n// @description  深度修复多项解析与显示问题 (股权/项目/任务/互联网/NPC)，改善多NPC信息完整性与重复问题，优化AI位置与总结计数逻辑。\n// <AUTHOR> (为您定制)\n// @grant        GM_setValue\n// @grant        GM_getValue\n// @grant        GM_deleteValue\n// @grant        GM_listValues \n// @grant        GM_xmlhttpRequest \n// @inject-into  content\n// @require      https://code.jquery.com/jquery-3.7.1.min.js\n// ==/UserScript==\n\n'use strict';\n/* global SillyTavern, 추출, getContext, getChatMessages, replaceVariables, getVariables, eventOn, tavern_events, retrieveDisplayedMessage, saveChatVariable, loadChatVariable, getLorebookEntries, saveWorldInfoEntry, getWorldInfoEntries */\n/* globals jQuery, $ */\n\n(async function () {\n    console.log('[信息栏UPM v2.3.69] 脚本开始执行...');\n\n    const MAX_AI_NPCS = 4; \n    let aiFailedToOutputThinkbiaoCount = 0; \n    const SUMMARY_TEXT_START_TAG = '<summaryText'; \n    const SUMMARY_TEXT_END_TAG = '</summaryText>';\n    const SUMMARY_TEXT_REGEX = new RegExp( SUMMARY_TEXT_START_TAG + '[^>]*>([\\\\s\\\\S]*?)' + SUMMARY_TEXT_END_TAG, 'si' );\n\n\n    const INFO_CONFIG = { \n        panels: { \n            generalSettings: { id: 'generalSettings', label: '⚙️ 基础设置', icon: 'fa-cogs', isUtilityPanel: true, description: '信息栏的基础功能与外观设置。', defaultPanelEnabled: true, items: [] },\n            worldTime: { id: 'worldTime', label: '🌍 世界时间', icon: 'fa-clock', chatVarPanelEnabled: 'infobar_panel_worldtime_enabled', description: '当前世界的时间与天气信息 (在信息栏顶部显示)。', defaultPanelEnabled: true, items: [ { id: 'date', label: '日期', defaultEnabled: true, chatVarItemEnabled: 'infobar_worldtime_date_enabled', defaultDisplayValue: \"(日期未知)\" }, { id: 'time', label: '时间', defaultEnabled: true, chatVarItemEnabled: 'infobar_worldtime_time_enabled', defaultDisplayValue: \"(时间未知)\" }, { id: 'weather', label: '天气', defaultEnabled: true, chatVarItemEnabled: 'infobar_worldtime_weather_enabled', defaultDisplayValue: \"(天气未知)\" } ] },\n            personal: { id: 'personal', label: '👤 个人信息', icon: 'fa-user-circle', chatVarPanelEnabled: 'infobar_panel_personal_enabled', description: '关于角色自身的基础信息。', defaultPanelEnabled: true, items: [ { id: 'name', label: '姓名', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_name_enabled', defaultDisplayValue: \"(AI应主动设定姓名)\" }, { id: 'age', label: '年龄', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_age_enabled', defaultDisplayValue: \"(AI应主动设定年龄)\" , isNumeric: true, formatUnit: false }, { id: 'gender', label: '性别', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_gender_enabled', defaultDisplayValue: \"(AI应主动设定性别)\" }, { id: 'race', label: '种族/物种', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_race_enabled', defaultDisplayValue: \"(AI应主动设定种族)\" }, { id: 'currentLocation', label: '当前位置', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_currentLocation_enabled', defaultDisplayValue: \"(AI应根据对话设定位置)\" }, { id: 'status', label: '身体状态', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_status_enabled', defaultDisplayValue: \"良好\" }, { id: 'mood', label: '情绪', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_mood_enabled', defaultDisplayValue: \"平静\" }, { id: 'funds', label: '个人资金', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_funds_enabled', defaultDisplayValue: \"0.00\" , isNumeric: true, formatUnit: true }, { id: 'points', label: '系统/特殊积分', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_points_enabled', defaultDisplayValue: \"0\", isNumeric: true, formatUnit: true }, { id: 'appearance', label: '外貌简述', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_appearance_enabled', defaultDisplayValue: \"(AI应主动描述外貌)\" }, { id: 'personality', label: '个性', defaultEnabled: true, chatVarItemEnabled: 'infobar_personal_personality_enabled', defaultDisplayValue: \"(AI应主动描述个性)\" }, {id: 'health', label: '健康状况', defaultEnabled: false, chatVarItemEnabled: 'infobar_personal_health_enabled', defaultDisplayValue: \"良好\"} ] },\n            company: { \n                id: 'company', \n                label: '🏢 组织/公司', \n                icon: 'fa-building', \n                chatVarPanelEnabled: 'infobar_panel_company_enabled', \n                description: '关于角色当前主要关注的组织/公司信息。', \n                defaultPanelEnabled: true, \n                items: [ \n                    { id: 'name', label: '组织/公司名称', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_name_enabled', defaultDisplayValue: \"(暂无组织)\" },  \n                    { id: 'type', label: '组织类型', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_type_enabled', defaultDisplayValue: \"(类型未知)\" },  \n                    { id: 'status', label: '当前状态', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_status_enabled', defaultDisplayValue: \"(运营中)\" }, \n                    { id: 'mainBusiness', label: '主要业务/产品', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_mainBusiness_enabled', defaultDisplayValue: \"(AI设定主要业务)\" }, \n                    { id: 'employeeCount', label: '员工数量', defaultEnabled: false, chatVarItemEnabled: 'infobar_company_employeeCount_enabled', defaultDisplayValue: \"(待补充)\", isNumeric: true, formatUnit: false }, \n                    { id: 'hqLocation', label: '总部地点', defaultEnabled: false, chatVarItemEnabled: 'infobar_company_hqLocation_enabled', defaultDisplayValue: \"(AI设定总部地点)\" }, \n                    { id: 'valuation', label: '公司估值', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_valuation_enabled', defaultDisplayValue: \"(待补充)\", isNumeric: true, formatUnit: true },  \n                    { id: 'reputation', label: '声望/影响力', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_reputation_enabled', defaultDisplayValue: \"普通\" },  \n                    { id: 'funds', label: '组织资金', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_funds_enabled', defaultDisplayValue: \"(待补充)\", isNumeric: true, formatUnit: true },  \n                    { id: 'shareholders', label: '股权结构', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_shareholders_enabled', isComplex: true, complexType: 'shareholdersList', defaultDisplayValue: '[]' },  \n                    { id: 'projects', label: '进行中项目', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_projects_enabled', isComplex: true, complexType: 'projectsListSimple', defaultDisplayValue: '[]' }, \n                    { id: 'recentEvents', label: '近期事件/新闻', defaultEnabled: true, chatVarItemEnabled: 'infobar_company_recentEvents_enabled', isComplex: true, complexType: 'stringListSimple', defaultDisplayValue: '[]' }, // using stringListSimple\n                    { id: 'rivals', label: '主要竞争对手', defaultEnabled: false, chatVarItemEnabled: 'infobar_company_rivals_enabled', isComplex: true, complexType: 'rivalsList', defaultDisplayValue: '[]' }, \n                    {id: 'shares', label: '股份占比(主角)', defaultEnabled: false, chatVarItemEnabled: 'infobar_company_shares_enabled', defaultDisplayValue: '(不适用)'} \n                ] \n            },\n            interactionDisplay: { \n                id: 'interactionDisplay', \n                label: '🎯 交互对象', \n                icon: 'fa-users', \n                chatVarPanelEnabled: 'infobar_panel_interactiondisplay_enabled', \n                description: '显示当前场景中主要NPC的信息。使用下拉框切换查看已记录的对象。', \n                defaultPanelEnabled: true, \n                npcObjectStructure: [ \n                    { id: 'name', label: '姓名', defaultEnabled: true, isCore: true, chatVarItemEnabled: 'infobar_interactiondisplay_name_enabled', defaultDisplayValue: \"(未选定)\"},\n                    { id: 'identity', label: '身份/职业', defaultEnabled: true, isCore: true, chatVarItemEnabled: 'infobar_interactiondisplay_identity_enabled', defaultDisplayValue: \"-\" },\n                    { id: 'mood', label: '情绪', defaultEnabled: true, isCore: true, chatVarItemEnabled: 'infobar_interactiondisplay_mood_enabled', defaultDisplayValue: \"-\" },\n                    { id: 'currentState', label: '当前状态/动作', defaultEnabled: true, isCore: true, chatVarItemEnabled: 'infobar_interactiondisplay_currentState_enabled', defaultDisplayValue: \"-\" },\n                    { id: 'affectionToUser', label: '对主角好感度', defaultEnabled: true, isNumeric: true, defaultDisplayValue: \"-/-\", chatVarItemEnabled: 'infobar_interactiondisplay_affectionToUser_enabled' },\n                    { id: 'relationshipToUser', label: '与主角关系', defaultEnabled: true, chatVarItemEnabled: 'infobar_interactiondisplay_relationshipToUser_enabled', defaultDisplayValue: \"(未知)\" }, \n                    { id: 'goodWill', label: '善意/忠诚度', defaultEnabled: true, chatVarItemEnabled: 'infobar_interactiondisplay_goodWill_enabled', defaultDisplayValue: \"中立\"},\n                    { id: 'emotional_status', label: '详细情感状态', defaultEnabled: true, chatVarItemEnabled: 'infobar_interactiondisplay_emotional_status_enabled', defaultDisplayValue: \"{}\", isComplex: true, complexType: 'jsonString'},\n                    { id: 'upperBody', label: '上身穿着', defaultEnabled: true, chatVarItemEnabled: 'infobar_interactiondisplay_upperBody_enabled', defaultDisplayValue: \"(未描述)\" }, \n                    { id: 'lowerBody', label: '下身穿着', defaultEnabled: true, chatVarItemEnabled: 'infobar_interactiondisplay_lowerBody_enabled', defaultDisplayValue: \"(未描述)\" }, \n                    { id: 'footwear', label: '鞋袜', defaultEnabled: true, chatVarItemEnabled: 'infobar_interactiondisplay_footwear_enabled', defaultDisplayValue: \"(未描述)\" },          \n                    { id: 'clothing', label: '整体穿着描述', defaultEnabled: false, chatVarItemEnabled: 'infobar_interactiondisplay_clothing_enabled', defaultDisplayValue: \"-\" }, \n                    { id: 'physicalFeatures', label: '身体特征', defaultEnabled: true, chatVarItemEnabled: 'infobar_interactiondisplay_physicalFeatures_enabled', defaultDisplayValue: \"(无特别描述)\" }, \n                    { id: 'hobbies', label: '爱好', defaultEnabled: true, chatVarItemEnabled: 'infobar_interactiondisplay_hobbies_enabled', defaultDisplayValue: \"[]\", isComplex: true, complexType: 'stringListSimple' },  // Using stringListSimple\n                    { id: 'shameLevel', label: '羞耻度', defaultEnabled: true, isNumeric: true, defaultDisplayValue: \"-/-\", chatVarItemEnabled: 'infobar_interactiondisplay_shameLevel_enabled' }, \n                    { id: 'angerLevel', label: '愤怒度', defaultEnabled: true, isNumeric: true, defaultDisplayValue: \"-/-\", chatVarItemEnabled: 'infobar_interactiondisplay_angerLevel_enabled' }, \n                    { id: 'vaginalStatus', label: '阴道状态 (NSFW)', defaultEnabled: false, isSensitive: true, chatVarItemEnabled: 'infobar_interactiondisplay_vaginalStatus_enabled', defaultDisplayValue: \"-\" },\n                    { id: 'breastStatus', label: '乳房状态 (NSFW)', defaultEnabled: false, isSensitive: true, chatVarItemEnabled: 'infobar_interactiondisplay_breastStatus_enabled', defaultDisplayValue: \"-\" },\n                    { id: 'penisStatus', label: '阴茎状态 (NSFW)', defaultEnabled: false, isSensitive: true, chatVarItemEnabled: 'infobar_interactiondisplay_penisStatus_enabled', defaultDisplayValue: \"-\" },\n                    { id: 'analStatus', label: '肛门状态 (NSFW)', defaultEnabled: false, isSensitive: true, chatVarItemEnabled: 'infobar_interactiondisplay_analStatus_enabled', defaultDisplayValue: \"-\" },\n                    { id: 'oralStatus', label: '口腔状态 (NSFW)', defaultEnabled: false, isSensitive: true, chatVarItemEnabled: 'infobar_interactiondisplay_oralStatus_enabled', defaultDisplayValue: \"-\" },\n                ],\n                items: [] \n            },\n            focusAffectionNPC: { id: 'focusAffectionNPC',label: '💖 焦点好感度',icon: 'fa-heartbeat',chatVarPanelEnabled: 'infobar_panel_focusaffectionnpc_enabled',description: '追踪当前对话中好感度变化最明显或用户最关注的NPC的好感度。',defaultPanelEnabled: true,items: [{ id: 'name', label: 'NPC姓名', defaultEnabled: true, chatVarItemEnabled: 'infobar_focusaffectionnpc_name_enabled', defaultDisplayValue: \"(无焦点对象)\" },{ id: 'value', label: '好感度值', defaultEnabled: true, chatVarItemEnabled: 'infobar_focusaffectionnpc_value_enabled', defaultDisplayValue: \"50\" },{ id: 'max', label: '最大值', defaultEnabled: true, chatVarItemEnabled: 'infobar_focusaffectionnpc_max_enabled', defaultDisplayValue: \"100\" },{ id: 'status', label: '状态描述', defaultEnabled: true, chatVarItemEnabled: 'infobar_focusaffectionnpc_status_enabled', defaultDisplayValue: \"(中立)\" },] },\n            affectionPanel: { id: 'affectionPanel', label: '💖 好感度追踪 (列表)', icon: 'fa-heart', chatVarPanelEnabled: 'infobar_panel_affectionlist_enabled', description: '以列表形式追踪多个NPC或派系的好感度。AI仍会填充此列表。', defaultPanelEnabled: false, isListPanel: true, itemKeyPrefix: 'affectionPanel.', defaultDisplayValue: '[]' }, \n            warehousePanel: { id: 'warehousePanel', label: '📦 仓库资源', icon: 'fa-warehouse', chatVarPanelEnabled: 'infobar_panel_warehouse_enabled', description: '存储的各类资源和材料。', defaultPanelEnabled: false, isListPanel: true, itemKeyPrefix: 'warehousePanel.', defaultDisplayValue: '[]' },\n            tasks: { id: 'tasks', label: '🎯 任务与目标', icon: 'fa-tasks', chatVarPanelEnabled: 'infobar_panel_tasks_enabled', description: '当前的主要任务、支线任务和每日目标。', defaultPanelEnabled: true, items: [ { id: 'mainQuest', label: '主线任务', defaultEnabled: true, chatVarItemEnabled: 'infobar_tasks_mainQuest_enabled', isComplex: true, complexType: 'task', defaultDisplayValue: '{\"name\":\"(暂无主线)\",\"status\":\"-\"}' }, { id: 'sideQuests', label: '支线任务', defaultEnabled: true, chatVarItemEnabled: 'infobar_tasks_sideQuests_enabled', isComplex: true, complexType: 'taskList', defaultDisplayValue: '[]' }, { id: 'dailyTask', label: '每日任务', defaultEnabled: false, chatVarItemEnabled: 'infobar_tasks_dailyTask_enabled', isComplex: true, complexType: 'task', defaultDisplayValue: '{\"name\":\"(暂无每日)\",\"status\":\"-\"}' }, ] },\n            environment: { id: 'environment', label: '🌍 环境信息', icon: 'fa-map-marker-alt', chatVarPanelEnabled: 'infobar_panel_environment_enabled', description: '当前场景的环境细节。', defaultPanelEnabled: true, items: [ { id: 'location', label: '地点', defaultEnabled: true, chatVarItemEnabled: 'infobar_environment_location_enabled', defaultDisplayValue: \"(地点未知)\" }, { id: 'nearbyCharacters', label: '周边角色', defaultEnabled: true, chatVarItemEnabled: 'infobar_environment_nearbyCharacters_enabled', defaultDisplayValue: \"无\" }, { id: 'ambiance', label: '氛围', defaultEnabled: true, chatVarItemEnabled: 'infobar_environment_ambiance_enabled', defaultDisplayValue: \"普通\" }, { id: 'sound', label: '声音', defaultEnabled: true, chatVarItemEnabled: 'infobar_environment_sound_enabled', defaultDisplayValue: \"安静\" }, ] },\n            clothing: { id: 'clothing', label: '👕 服装与外观', icon: 'fa-tshirt', chatVarPanelEnabled: 'infobar_panel_clothing_enabled', description: '角色的当前着装和姿态。', defaultPanelEnabled: true, items: [ { id: 'upperBody', label: '上身', defaultEnabled: true, chatVarItemEnabled: 'infobar_clothing_upperBody_enabled', defaultDisplayValue: \"(未描述)\" }, { id: 'lowerBody', label: '下身', defaultEnabled: true, chatVarItemEnabled: 'infobar_clothing_lowerBody_enabled', defaultDisplayValue: \"(未描述)\" }, { id: 'posture', label: '姿态', defaultEnabled: true, chatVarItemEnabled: 'infobar_clothing_posture_enabled', defaultDisplayValue: \"站立\" }, { id: 'heldItem', label: '手持物品', defaultEnabled: true, chatVarItemEnabled: 'infobar_clothing_heldItem_enabled', defaultDisplayValue: \"无\" }, ] },\n            abilities: { id: 'abilities', label: '✨ 能力', icon: 'fa-magic', chatVarPanelEnabled: 'infobar_panel_abilities_enabled', description: '角色的特殊能力和已习得技能。', defaultPanelEnabled: true, items: [ { id: 'specialAbility', label: '特殊能力', defaultEnabled: true, chatVarItemEnabled: 'infobar_abilities_specialAbility_enabled', isComplex: true, complexType: 'abilitiesList', defaultDisplayValue: '[]' },  { id: 'learnedSkills', label: '已习得技能', defaultEnabled: true, chatVarItemEnabled: 'infobar_abilities_learnedSkills_enabled', isComplex: true, complexType: 'abilitiesList', defaultDisplayValue: '[]' } ] },\n            shop: { id: 'shop', label: '🛒 系统商城', icon: 'fa-store', chatVarPanelEnabled: 'infobar_panel_shop_enabled', description: '当前可购买的商品或服务。', defaultPanelEnabled: true, items: [ { id: 'currencyName', label: '商城货币', defaultEnabled: true, chatVarItemEnabled: 'infobar_shop_currencyName_enabled', defaultDisplayValue: \"金币\" }, { id: 'featuredItem1', label: '推荐商品1', defaultEnabled: true, chatVarItemEnabled: 'infobar_shop_featuredItem1_enabled', isComplex: true, complexType: 'shopItem', defaultDisplayValue: '{\"name\":\"(空栏位)\"}' }, { id: 'featuredItem2', label: '推荐商品2', defaultEnabled: true, chatVarItemEnabled: 'infobar_shop_featuredItem2_enabled', isComplex: true, complexType: 'shopItem', defaultDisplayValue: '{\"name\":\"(空栏位)\"}' }, { id: 'featuredItem3', label: '推荐商品3', defaultEnabled: false, chatVarItemEnabled: 'infobar_shop_featuredItem3_enabled', isComplex: true, complexType: 'shopItem', defaultDisplayValue: '{\"name\":\"(空栏位)\"}' }, { id: 'refreshTime', label: '下次刷新', defaultEnabled: true, chatVarItemEnabled: 'infobar_shop_refreshTime_enabled', defaultDisplayValue: \"很快\" }, { id: 'shopNotice', label: '商店公告', defaultEnabled: false, chatVarItemEnabled: 'infobar_shop_shopNotice_enabled', defaultDisplayValue: \"\" }, ] },\n            system: { \n                id: 'system', \n                label: '⚙️ 系统面板 (示例)', \n                icon: 'fa-desktop', \n                chatVarPanelEnabled: 'infobar_panel_system_enabled', \n                description: '游戏或故事中的系统相关信息。', \n                defaultPanelEnabled: false, \n                items: [\n                    { id: 'time', label: '系统时间', defaultEnabled: true, chatVarItemEnabled: 'infobar_system_time_enabled', defaultDisplayValue: \"(未知)\" },\n                    { id: 'location', label: '系统判定地点', defaultEnabled: true, chatVarItemEnabled: 'infobar_system_location_enabled', defaultDisplayValue: \"(未知)\" },\n                    { id: 'funds', label: '系统货币', defaultEnabled: true, chatVarItemEnabled: 'infobar_system_funds_enabled', defaultDisplayValue: \"0\", isNumeric: true, formatUnit: true },\n                    { id: 'points', label: '系统积分', defaultEnabled: true, chatVarItemEnabled: 'infobar_system_points_enabled', defaultDisplayValue: \"0\", isNumeric: true, formatUnit: true },\n                    { id: 'techTree', label: '科技树状态', defaultEnabled: false, chatVarItemEnabled: 'infobar_system_techTree_enabled', defaultDisplayValue: \"(未解锁)\", isComplex: true, complexType: 'jsonString' },\n                    { id: 'tasks', label: '系统任务列表', defaultEnabled: true, chatVarItemEnabled: 'infobar_system_tasks_enabled', isComplex: true, complexType: 'taskListSystem', defaultDisplayValue: \"[]\" }, \n                    { id: 'company.name', label: '系统关联公司名', defaultEnabled: false, chatVarItemEnabled: 'infobar_system_company.name_enabled', defaultDisplayValue: \"(无)\" }, \n                    { id: 'company.status', label: '系统关联公司状态', defaultEnabled: false, chatVarItemEnabled: 'infobar_system_company.status_enabled', defaultDisplayValue: \"(无)\" },\n                    { id: 'company.projects', label: '系统关联公司项目', defaultEnabled: false, chatVarItemEnabled: 'infobar_system_company.projects_enabled', isComplex: true, complexType: 'stringListSimple', defaultDisplayValue: \"[]\" },\n                ] \n            },\n            internet: { \n                id: 'internet',\n                label: '🌐 互联网资讯',\n                icon: 'fa-wifi',\n                chatVarPanelEnabled: 'infobar_panel_internet_enabled',\n                description: '模拟的互联网新闻或社交媒体信息流。',\n                defaultPanelEnabled: false, \n                items: [ \n                    { id: 'newsItems', label: '显示资讯列表', defaultEnabled: true, chatVarItemEnabled: 'infobar_internet_newsItems_enabled', isComplex: true, complexType: 'newsFeed', defaultDisplayValue: \"[]\" },\n                    { id: 'time', label: '互联网时间', defaultEnabled: false, chatVarItemEnabled: 'infobar_internet_time_enabled', defaultDisplayValue: \"(未知)\" },\n                    { id: 'weather', label: '互联网地点天气', defaultEnabled: false, chatVarItemEnabled: 'infobar_internet_weather_enabled', defaultDisplayValue: \"(未知)\" },\n                    { id: 'socialFeeds', label: '社交媒体流', defaultEnabled: false, chatVarItemEnabled: 'infobar_internet_socialFeeds_enabled', isComplex: true, complexType: 'stringListSimple', defaultDisplayValue: \"[]\" },\n                    { id: 'forumThreads', label: '热门论坛帖子', defaultEnabled: false, chatVarItemEnabled: 'infobar_internet_forumThreads_enabled', isComplex: true, complexType: 'stringListSimple', defaultDisplayValue: \"[]\" }\n                ]\n            },\n            summary: { \n                id: 'summary',\n                label: '📝 剧情总结',\n                icon: 'fa-file-alt',\n                chatVarPanelEnabled: 'infobar_panel_summary_enabled',\n                description: '设置自动剧情总结功能，并查看总结状态。总结内容会保存到世界书。',\n                defaultPanelEnabled: true,\n                items: [\n                    { id: 'enableBigSummary', label: '启用大总结', type: 'toggle', defaultEnabled: true, chatVarItemEnabled: 'infobar_summary_enableBigSummary_enabled' },\n                    { id: 'bigSummaryInterval', label: '大总结间隔(AI回复数)', type: 'number', defaultDisplayValue: \"30\", chatVarItemEnabled: 'infobar_summary_bigSummaryInterval_enabled', defaultEnabled: true },\n                    { id: 'enableSmallSummary', label: '启用小总结', type: 'toggle', defaultEnabled: true, chatVarItemEnabled: 'infobar_summary_enableSmallSummary_enabled' },\n                    { id: 'smallSummaryInterval', label: '小总结间隔(AI回复数)', type: 'number', defaultDisplayValue: \"10\", chatVarItemEnabled: 'infobar_summary_smallSummaryInterval_enabled', defaultEnabled: true },\n                    { id: 'status', label: '总结状态', type: 'display', defaultEnabled: true, chatVarItemEnabled: 'infobar_summary_status_enabled', defaultDisplayValue: \"(等待首次触发)\"}, \n                    { id: 'lastBigSummaryTitle', label: '上次大总结(世界书)', type: 'display', defaultEnabled: true, chatVarItemEnabled: 'infobar_summary_lastBigSummaryTitle_enabled', defaultDisplayValue: \"(无)\"},\n                    { id: 'lastSmallSummaryTitle', label: '上次小总结(世界书)', type: 'display', defaultEnabled: true, chatVarItemEnabled: 'infobar_summary_lastSmallSummaryTitle_enabled', defaultDisplayValue: \"(无)\"},\n                    { id: 'manualSummaryTrigger', label: '手动触发总结', type: 'button', defaultEnabled: true, chatVarItemEnabled: 'infobar_summary_manualSummaryTrigger_enabled', buttonText: \"立即总结小段剧情\"}\n                ]\n            },\n            backupManagement: { id: 'backupManagement', label: '💾 数据管理', icon: 'fa-database', isUtilityPanel: true, description: '管理当前角色的信息栏数据 (存储于聊天变量中)。', defaultPanelEnabled: true, items: [] }\n        },\n        storageKeySettings: 'sillytavern_infobar_settings_v23', // Version bump for settings if structure changed significantly\n        chatVarKeyForInfoBarData: 'infobar_upm_character_data_v1', \n        storageKeyTheme: 'sillytavern_infobar_upm_theme_v1'\n    };\n    const BUTTON_ID = 'infobar-upm-button-v2369'; \n    const NPC_SELECTOR_ID = 'infobar-npc-selector-v2369';\n    const NPC_DETAIL_CONTAINER_ID = 'infobar-npc-detail-container-v2369';\n    const AI_THINK_PROCESS_START_TAG = '<aiThinkProcess>';\n    const AI_THINK_PROCESS_END_TAG = '</aiThinkProcess>';   \n    const AI_THINK_PROCESS_REGEX = new RegExp( AI_THINK_PROCESS_START_TAG.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '([\\\\s\\\\S]*?)' + AI_THINK_PROCESS_END_TAG.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'si' ); \n    const BUTTON_ICON = 'fa-solid fa-info-circle';\n    const BUTTON_TOOLTIP = '信息栏 UPM 设置 v2.3.69';\n    const BUTTON_TEXT = '信息栏 UPM';\n    const POPUP_ID = 'infobar-upm-popup-v2369';\n    const RENDERED_INFO_BAR_CLASS = 'infobar-upm-rendered-container';\n    const TOP_TIME_DISPLAY_CLASS = 'infobar-upm-top-time-display';\n    const BACKUP_STATUS_WIDGET_ID = 'infobar-upm-backup-status-widget'; \n    const AI_DATA_START_TAG = '<thinkbiao>';\n    const AI_DATA_END_TAG = '</thinkbiao>';\n    const AI_DATA_BLOCK_REGEX = new RegExp( AI_DATA_START_TAG.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '([\\\\s\\\\S]*?)' + AI_DATA_END_TAG.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'si' );\n    const AI_KEY_VALUE_REGEX = /^([a-zA-Z0-9]+(?:[1-9]\\d*)?(?:\\.[a-zA-Z0-9_]+)+):\\s*([\\s\\S]*?)$/; \n    const AI_PLACEHOLDERS = [\"(未知)\", \"N/A\", \"（未知）\", \"（暂无）\", \"(待定)\", \"(待补充)\", \"(不适用)\", \"无明确信息\", \"(空白)\", \"少量\", \"一些\", \"很多\", \"(请AI设定姓名)\", \"(请AI设定年龄)\", \"(请AI设定性别)\", \"(请AI设定种族)\", \"(请AI设定位置)\", \"(AI应根据对话设定位置)\", \"(请AI描述外貌)\", \"(请AI描述个性)\", \"(暂无数据)\", \"(数据待更新)\", \"(信息缺失)\", \"(暂无主线)\", \"(无支线)\", \"(暂无每日)\", \"(空栏位)\", \"暂无具体信息\", \"请AI设定\", \"(AI应主动设定\", \"(ai应主动设定\", \"(ai应根据对话设定\", \"(当前剧情无系统商城)\", \"(当前剧情无激活的系统商城)\", \"n/a\", \"(无可用商城)\", \"(暂无支线任务)\", \"(暂无项目信息)\", \"(暂无详细股权信息)\", \"(暂无好感度信息)\", \"仓库为空\", \"(日期未知)\", \"(时间未知)\", \"(天气未知)\", \"(AI设定)\", \"(不适用)\", \"(未提及)\", \"(未在当前情境中提及)\", \"(信息未提供或不适用)\", \"-\", \"(无当前对象)\", \"(无焦点对象)\", \"(无主要交互对象)\", \"(未选定)\"];\n\n\n    let currentSettings = {}; \n    let infoBarDefaultCollapsed = true;\n    let currentTheme = 'auto'; \n    let chatChangeDebounceTimer = null;\n    let lastKnownCharNameFromContext = \"(未知角色)\"; \n    let previousCharacterFile = null; \n\n    // --- 工具函数 ---\n    function errorCatched(fn, context = null, functionName = 'anonymous') { return (...args) => { try { const result = fn.apply(context, args); if (result instanceof Promise) { return result.catch(error => { console.error(`[信息栏UPM ASYNC_ERROR] in ${functionName}:`, error,error.stack); notifyUser(`[信息栏UPM] 脚本错误: ${error.message} (详情见控制台)`, 'error', 5000); }); } return result; } catch (error) { console.error(`[信息栏UPM SYNC_ERROR] in ${functionName}:`, error,error.stack); notifyUser(`[信息栏UPM] 脚本错误: ${error.message} (详情见控制台)`, 'error', 5000); } }; }\n    function notifyUser(message, type = 'info', duration = 3000) { if (typeof SillyTavern !== 'undefined' && SillyTavern.TemmuzAdalet && SillyTavern.TemmuzAdalet.NotifSuccess) { const options = { timeOut: duration }; try { switch (type) { case 'success': SillyTavern.TemmuzAdalet.NotifSuccess(message, options); break; case 'error': SillyTavern.TemmuzAdalet.NotifDanger(message, options); break; case 'warning': SillyTavern.TemmuzAdalet.NotifWarning(message, options); break; default: SillyTavern.TemmuzAdalet.NotifInfo(message, options); break; } } catch (e) { console.error(\"[信息栏UPM] SillyTavern.TemmuzAdalet notification failed:\", e); console.log(`[信息栏UPM Fallback Notification / ${type.toUpperCase()}]: ${message}`); } } else { console.log(`[信息栏UPM Notification / ${type.toUpperCase()}]: ${message}`); } }\n    function escapeHtml(unsafe) { if (unsafe === null || unsafe === undefined) return ''; if (typeof unsafe !== 'string') return String(unsafe); return unsafe.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#039;\"); }\n    function formatLargeNumber(numStr) { if (numStr === null || numStr === undefined) return \"(未提供)\"; const cleanedStr = String(numStr).replace(/[^0-9.-]/g, ''); const num = parseFloat(cleanedStr); if (isNaN(num)) return String(numStr); const absNum = Math.abs(num); const sign = num < 0 ? \"-\" : \"\"; if (absNum >= 100000000) return sign + (absNum / 100000000).toFixed(2) + '亿'; else if (absNum >= 10000) return sign + (absNum / 10000).toFixed(2) + '万'; return sign + num.toLocaleString(undefined, { minimumFractionDigits: (num % 1 === 0) ? 0 : 2, maximumFractionDigits: 2 }); }\n    function getDisplayCharacterName() { \n        if (lastKnownCharNameFromContext && lastKnownCharNameFromContext !== \"(未知角色)\") {\n            return lastKnownCharNameFromContext;\n        }\n        if (SillyTavern && SillyTavern.getContext) { \n            const context = SillyTavern.getContext(); \n            let name = context?.name || context?.character_name || context?.characterFilename; \n            if (name) { \n                if (typeof name === 'string' && (name.endsWith('.json') || name.endsWith('.yaml') || name.endsWith('.png'))) { \n                    name = name.substring(0, name.lastIndexOf('.')); \n                } \n                lastKnownCharNameFromContext = name.replace(/_/g, ' '); \n                return lastKnownCharNameFromContext; \n            } \n        } \n        return \"(未知角色)\"; \n    }\n\n    // --- Theme Management ---\n    function applyTheme(theme) { errorCatched(() => { const pD = window.parent.document; if (!pD) { console.warn(\"[信息栏UPM] applyTheme: Parent document not found.\"); return; } const $popup = $(`#${POPUP_ID}`, pD); const $body = $('body', pD); $popup.removeClass('light dark cyberpunk steampunk minimalist-modern parchment-magical'); const $allInfoBarContainers = $(`.${RENDERED_INFO_BAR_CLASS}`, pD); $allInfoBarContainers.removeClass('theme-light theme-dark theme-cyberpunk theme-steampunk theme-minimalist-modern theme-parchment-magical'); let finalTheme = theme; if (theme === 'auto') { finalTheme = ($body.hasClass('dark_mode') || $body.hasClass('dark')) ? 'dark' : 'light'; } $popup.addClass(finalTheme); $allInfoBarContainers.addClass(`theme-${finalTheme}`); currentTheme = theme; localStorage.setItem(INFO_CONFIG.storageKeyTheme, theme); const $visibleMessages = $('#chat .mes[style*=\"display: block;\"], #chat .mes:not([style*=\"display: none;\"])', pD); $visibleMessages.each(function() { const $messageNode = $(this); if ($messageNode.find(`.${RENDERED_INFO_BAR_CLASS}`).length > 0) { const msgId = $messageNode.attr('id')?.replace('chat_msg_', '') || $messageNode.attr('mesid'); if (msgId) { $messageNode.find(`.${RENDERED_INFO_BAR_CLASS}`).remove(); $messageNode.find(`.${TOP_TIME_DISPLAY_CLASS}`).remove(); $messageNode.find(`style[data-infobar-style]`).remove(); handleMessageRendering(msgId); } } }); }, null, 'applyTheme')(); }\n    function loadTheme() { errorCatched(() => { const savedTheme = localStorage.getItem(INFO_CONFIG.storageKeyTheme); currentTheme = savedTheme || 'auto'; }, null, 'loadTheme')(); }\n\n    // --- 数据持久化 (Using Chat Variables primarily) ---\n    async function saveInfoBarDataToChatVars(data) { await errorCatched(async () => { if (!data || Object.keys(data).length === 0) return; if (typeof getVariables !== 'function' || typeof replaceVariables !== 'function') { console.error(\"[信息栏UPM saveInfoBarDataToChatVars] Chat variable functions not available!\"); return; } let chatVars = await getVariables({ type: 'chat' }) || {}; chatVars[INFO_CONFIG.chatVarKeyForInfoBarData] = data; await replaceVariables(chatVars, { type: 'chat' }); updateBackupStatusWidget(\"已存至聊天变量\", \"success\"); }, null, 'saveInfoBarDataToChatVars')(); }\n    async function loadInfoBarDataFromChatVars() { return await errorCatched(async () => { if (typeof getVariables !== 'function') { console.error(\"[信息栏UPM loadInfoBarDataFromChatVars] getVariables function not available!\"); updateBackupStatusWidget(\"聊天变量功能异常\", \"error\"); return null; } const chatVars = await getVariables({ type: 'chat' }); const data = chatVars ? chatVars[INFO_CONFIG.chatVarKeyForInfoBarData] : null; if (data && Object.keys(data).length > 0) { updateBackupStatusWidget(\"已从聊天变量加载\", \"info\"); return data; } else { updateBackupStatusWidget(\"无聊天变量数据\", \"info\"); return null; } }, null, 'loadInfoBarDataFromChatVars')(); }\n    async function clearCurrentCharInfoBarData(showConfirmation = true) { \n        await errorCatched(async () => { \n            if (typeof getVariables !== 'function' || typeof replaceVariables !== 'function') { \n                console.error(\"[信息栏UPM clearCurrentCharInfoBarData] Chat variable functions not available!\"); \n                if(showConfirmation) notifyUser(\"聊天变量功能异常，无法清除数据。\", \"error\"); \n                return; \n            } \n            const doClear = showConfirmation ? confirm(\"确定要清除当前角色的信息栏数据吗？此操作会清空当前聊天会话中保存的所有信息栏状态，包括所有NPC追踪数据和用户选择。\") : true; \n            if (doClear) { \n                let chatVars = await getVariables({ type: 'chat' }) || {}; \n                let didClearMain = false;\n\n                if (chatVars[INFO_CONFIG.chatVarKeyForInfoBarData]) { \n                    delete chatVars[INFO_CONFIG.chatVarKeyForInfoBarData]; \n                    didClearMain = true;\n                } \n                \n                if (didClearMain) { \n                    await replaceVariables(chatVars, { type: 'chat' }); \n                    if(showConfirmation) notifyUser(\"当前角色的信息栏数据已清除。\", \"success\"); \n                    updateBackupStatusWidget(\"数据已清除 (聊天变量)\", \"info\"); \n                    console.log(`[信息栏UPM INFO] Cleared chat variable '${INFO_CONFIG.chatVarKeyForInfoBarData}' for current chat.`); \n                } else { \n                    if(showConfirmation) notifyUser(\"当前角色没有可清除的信息栏数据。\", \"info\"); \n                }\n            } \n        }, null, 'clearCurrentCharInfoBarData')(); \n    }\n    \n    // --- 设置加载/保存 ---\n    async function loadSettings() {\n        await errorCatched(async () => {\n            currentSettings = {};\n            const saved = localStorage.getItem(INFO_CONFIG.storageKeySettings);\n            const parsedSettings = saved ? JSON.parse(saved) : {};\n    \n            for (const panelId in INFO_CONFIG.panels) {\n                const panel_config = INFO_CONFIG.panels[panelId]; \n                if (!panel_config || panel_config.isUtilityPanel) continue;\n                \n                currentSettings[panelId] = { \n                    enabled: parsedSettings[panelId]?.enabled ?? panel_config.defaultPanelEnabled, \n                    itemsEnabled: {} \n                };\n                \n                let itemsToProcess = panel_config.items; \n                if (panelId === 'interactionDisplay' && panel_config.npcObjectStructure) {\n                    itemsToProcess = panel_config.npcObjectStructure;\n                }\n\n                if (itemsToProcess && Array.isArray(itemsToProcess)) {\n                    currentSettings[panelId].itemsEnabled = itemsToProcess.reduce((acc, itemConfig) => {\n                        const itemIdStr = String(itemConfig.id);\n                        acc[itemIdStr] = parsedSettings[panelId]?.itemsEnabled?.[itemIdStr] ?? itemConfig.defaultEnabled;\n                        return acc;\n                    }, {});\n                }\n            }\n            infoBarDefaultCollapsed = parsedSettings.generalSettings?.infoBarDefaultCollapsed ?? true;\n        }, null, 'loadSettings')();\n    }    \n    function saveSettings() { return errorCatched(() => { const settingsToSave = { ...currentSettings }; settingsToSave.generalSettings = { infoBarDefaultCollapsed: infoBarDefaultCollapsed, theme: currentTheme }; localStorage.setItem(INFO_CONFIG.storageKeySettings, JSON.stringify(settingsToSave)); updateChatVariablesFromSettings(); return true; }, null, 'saveSettings')(); }\n    async function updateChatVariablesFromSettings() { \n        await errorCatched(async () => { \n            if (typeof getVariables !== 'function' || typeof replaceVariables !== 'function') return; \n            let chatVars = await getVariables({ type: 'chat' }) || {}; \n            let changed = false; \n            for (const panelId in currentSettings) { \n                const panelConfig = INFO_CONFIG.panels[panelId]; \n                if (!panelConfig || panelConfig.isUtilityPanel) continue; \n                const panelSettings = currentSettings[panelId]; \n                if (panelConfig.chatVarPanelEnabled && chatVars[panelConfig.chatVarPanelEnabled] !== panelSettings.enabled) { \n                    chatVars[panelConfig.chatVarPanelEnabled] = panelSettings.enabled; \n                    changed = true; \n                } \n                \n                let itemsToProcess = panelConfig.items;\n                if (panelId === 'interactionDisplay' && panelConfig.npcObjectStructure) {\n                    itemsToProcess = panelConfig.npcObjectStructure;\n                }\n\n                if (itemsToProcess && Array.isArray(itemsToProcess)) { \n                    itemsToProcess.forEach(itemConfig => { \n                        if (itemConfig.chatVarItemEnabled) { \n                            const effectiveItemIsEnabled = panelSettings.enabled && panelSettings.itemsEnabled[itemConfig.id]; \n                            if (chatVars[itemConfig.chatVarItemEnabled] !== effectiveItemIsEnabled) { \n                                chatVars[itemConfig.chatVarItemEnabled] = effectiveItemIsEnabled; \n                                changed = true; \n                            } \n                        } \n                    }); \n                } \n            } \n            if (changed) await replaceVariables(chatVars, { type: 'chat' }); \n        }, null, 'updateChatVariablesFromSettings')(); \n    }\n\n\n    // --- UI 创建和管理 ---\n    function createBackupStatusWidget() { errorCatched(() => { if ($(`#${BACKUP_STATUS_WIDGET_ID}`).length > 0) return; const $widget = $(`<div id=\"${BACKUP_STATUS_WIDGET_ID}\" style=\"position: fixed; bottom: 5px; right: 10px; background-color: rgba(var(--bgOpaqueValue), 0.7); color: var(--text); padding: 5px 10px; border-radius: 4px; font-size: 11px; z-index: 9990; display: flex; align-items: center; gap: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.2); backdrop-filter: blur(2px);\"><span class=\"backup-widget-charname\" style=\"font-weight: bold; max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;\">角色: N/A</span><span class=\"backup-widget-status\">状态: 未知</span><button class=\"backup-widget-view-btn\" title=\"查看当前信息栏数据 (来自聊天变量)\" style=\"background:none;border:1px solid var(--primaryFaded);color:var(--text);padding:1px 4px;border-radius:3px;cursor:pointer;font-size:10px; line-height:1;\">查看</button></div>`); $('body').append($widget); $widget.find('.backup-widget-view-btn').on('click', errorCatched(async function() { const data = await loadInfoBarDataFromChatVars(); if (data) { const jsonData = JSON.stringify(data, null, 2); if (typeof SillyTavern !== 'undefined' && SillyTavern.callGenericPopup && SillyTavern.POPUP_TYPE) { SillyTavern.callGenericPopup( `<div style=\"white-space:pre-wrap; word-break:break-all; max-height:70vh; overflow-y:auto; background:var(--SillyTavernTextBox背景, #222); color:var(--textCr, #ddd); padding:10px; border-radius:4px;\">${escapeHtml(jsonData)}</div>`, SillyTavern.POPUP_TYPE.TEXT, `当前信息栏数据 (聊天变量)` ); } else { alert(\"信息栏数据:\\n\" + jsonData); } } else { notifyUser(\"当前聊天没有信息栏数据。\", \"info\"); } }, null, 'backupWidgetViewBtnClick')); updateBackupStatusWidget(); }, null, 'createBackupStatusWidget')(); }\n    async function updateBackupStatusWidget(statusText = null, type = 'info') { \n        await errorCatched(async () => { \n            const $widget = $(`#${BACKUP_STATUS_WIDGET_ID}`); \n            if (!$widget.length) return; \n            const charName = getDisplayCharacterName();\n            $widget.find('.backup-widget-charname').text(`${charName.substring(0,15)}${charName.length > 15 ? '...' : ''}`).attr('title', charName); \n            if (statusText === null) { \n                const data = await loadInfoBarDataFromChatVars(); \n                if (data && Object.keys(data).length > 0) { \n                    statusText = `已从聊天变量加载`; type = 'info'; \n                } else { \n                    statusText = \"无聊天变量数据\"; type = 'warning'; \n                } \n            } \n            const $statusEl = $widget.find('.backup-widget-status'); \n            $statusEl.text(`状态: ${statusText}`); \n            if (type === 'error') $statusEl.css('color', 'var(--Danger, lightcoral)'); \n            else if (type === 'success') $statusEl.css('color', 'var(--Success, lightgreen)'); \n            else $statusEl.css('color', 'var(--text, #ccc)'); \n        }, null, 'updateBackupStatusWidget')(); \n    }\n    function updateBackupPanelList() { errorCatched(() => { const pD = window.parent.document; const $listContainer = $('#infobar-backup-list-container', pD); if (!$listContainer.length) return; const currentCharacterName = getDisplayCharacterName(); $listContainer.html(` <div style=\"text-align:center; padding:10px;\"> <p>信息栏数据主要存储在当前聊天会话的变量中，与角色绑定。</p> <p>当前角色: <strong>${escapeHtml(currentCharacterName)}</strong></p> </div> <div class=\"infobar-item-row infobar-button-group\" style=\"justify-content: center;\"> <button id=\"infobar-view-current-chatvar-btn\" class=\"infobar-button-input\" style=\"flex-grow:0.5;\">查看当前角色数据</button> <button id=\"infobar-clear-current-char-data-btn\" class=\"infobar-button-input infobar-danger-button\" style=\"flex-grow:0.5;\">清除当前角色数据</button> </div> <hr style=\"margin: 15px 0; border-color: var(--infobar-border-color);\"> <p style=\"text-align:center; font-size:0.9em; color: var(--infobar-text-muted); padding:0 10px 10px 10px;\"> (未来版本可能提供基于localStorage的快照导入/导出功能。) </p> `); }, null, 'updateBackupPanelList')(); }\n    function createMenuButton() { errorCatched(() => { if (!window.parent || !window.parent.document) { setTimeout(createMenuButton, 1000); return; } const localParentDoc = window.parent.document; const buttonNamespace = `infobarUPMButton.${BUTTON_ID}`; const $extensionsMenu = $('#extensionsMenu', localParentDoc); if ($extensionsMenu.length === 0) { setTimeout(createMenuButton, 1000); return; } addGlobalStylesUPM(); $(`#${BUTTON_ID}`, localParentDoc).off(buttonNamespace).remove(); const buttonHtml = `<div id=\"${BUTTON_ID}\" class=\"list-group-item flex-container flexGap5 interactable\" title=\"${BUTTON_TOOLTIP}\" tabIndex=\"0\"><i class=\"${BUTTON_ICON}\"></i><span>${BUTTON_TEXT}</span></div>`; $extensionsMenu.append(buttonHtml); const $buttonElement = $(`#${BUTTON_ID}`, localParentDoc); if ($buttonElement.length) { $buttonElement.off('.directInfobarClick').on('click.directInfobarClick', function(e) { e.preventDefault(); e.stopPropagation(); showSettingsPopupUPM(); }); } else { console.error(`[信息栏UPM ERROR] Button #${BUTTON_ID} not found for DIRECT binding!`); } }, null, 'createMenuButton')(); }\n    async function showSettingsPopupUPM() { \n        await errorCatched(async () => { \n            await loadSettings(); \n            const localParentDoc = window.parent.document; \n            if (!localParentDoc) { console.error(\"[信息栏UPM ERROR] showSettingsPopupUPM: Parent document not found.\"); return; } \n            $(`#${POPUP_ID}`, localParentDoc).remove(); \n            $(`#${POPUP_ID}-overlay`, localParentDoc).remove(); \n            let panelTabsHtml = ''; \n            let panelContentHtml = ''; \n            let firstPanel = true; \n            for (const panelId in INFO_CONFIG.panels) { \n                const panel_config = INFO_CONFIG.panels[panelId]; \n                if (!panel_config) continue; \n                panelTabsHtml += `<button class=\"infobar-tab-button ${firstPanel ? 'active' : ''}\" data-tab=\"${panel_config.id}\"><i class=\"fa-solid ${panel_config.icon || 'fa-cog'}\"></i><span class=\"infobar-tab-label\">${escapeHtml(panel_config.label)}</span></button>`; \n                let itemsHtml = ''; \n                if (panel_config.id === 'generalSettings') { \n                    itemsHtml = ` <div class=\"infobar-item-row infobar-toggle-row\"> <label for=\"infobar-default-collapsed-toggle\" class=\"infobar-item-label\" style=\"font-weight:500;\">信息栏默认全部折叠</label> <div class=\"infobar-item-control\"><label class=\"infobar-switch\"><input type=\"checkbox\" id=\"infobar-default-collapsed-toggle\" ${infoBarDefaultCollapsed ? 'checked' : ''}><span class=\"infobar-slider round\"></span></label></div> </div> <div class=\"infobar-item-row\"> <label for=\"infobar-theme-select\" class=\"infobar-item-label\" style=\"font-weight:500;\">界面风格</label> <div class=\"infobar-item-control\"> <select id=\"infobar-theme-select\" class=\"infobar-select-input\"> <option value=\"auto\" ${currentTheme === 'auto' ? 'selected' : ''}>自动 (跟随酒馆)</option> <option value=\"light\" ${currentTheme === 'light' ? 'selected' : ''}>浅色</option> <option value=\"dark\" ${currentTheme === 'dark' ? 'selected' : ''}>深色</option> <option value=\"cyberpunk\" ${currentTheme === 'cyberpunk' ? 'selected' : ''}>赛博朋克</option> <option value=\"steampunk\" ${currentTheme === 'steampunk' ? 'selected' : ''}>蒸汽朋克</option> <option value=\"minimalist-modern\" ${currentTheme === 'minimalist-modern' ? 'selected' : ''}>简约现代</option> <option value=\"parchment-magical\" ${currentTheme === 'parchment-magical' ? 'selected' : ''}>羊皮纸/魔法</option> </select> </div> </div>`; \n                } else if (panel_config.id === 'summary') { \n                    const sumPanelConfig = INFO_CONFIG.panels.summary;\n                    const panelSettings = currentSettings.summary || {};\n                    const panelData = await loadInfoBarDataFromChatVars() || {}; \n                    const summaryData = panelData.summary || { bigSummaryInterval: 30, smallSummaryInterval: 10, lastBigSummaryTitle: \"(无)\", lastSmallSummaryTitle: \"(无)\" }; \n\n                    itemsHtml = sumPanelConfig.items.map(itemConfig => {\n                        const itemIdStr = String(itemConfig.id);\n                        const itemIsEnabled = panelSettings.itemsEnabled?.[itemIdStr] ?? itemConfig.defaultEnabled;\n                        const panelIsEnabled = panelSettings.enabled;\n\n                        if (itemConfig.type === 'toggle') {\n                             return `<div class=\"infobar-item-row infobar-toggle-row\"> <label for=\"infobar-item-${panel_config.id}-${itemIdStr}\" class=\"infobar-item-label\">${escapeHtml(itemConfig.label)}</label> <div class=\"infobar-item-control\"> <label class=\"infobar-switch\"> <input type=\"checkbox\" class=\"infobar-item-checkbox\" id=\"infobar-item-${panel_config.id}-${itemIdStr}\" data-panel-id=\"${panel_config.id}\" data-item-id=\"${itemIdStr}\" ${itemIsEnabled ? 'checked' : ''} ${!panelIsEnabled ? 'disabled' : ''}> <span class=\"infobar-slider round\"></span> </label> </div> </div>`;\n                        } else if (itemConfig.type === 'number') {\n                            const currentValue = summaryData[itemIdStr] !== undefined ? summaryData[itemIdStr] : parseInt(itemConfig.defaultDisplayValue, 10);\n                            return `<div class=\"infobar-item-row\"> <label for=\"infobar-item-${panel_config.id}-${itemIdStr}\" class=\"infobar-item-label\">${escapeHtml(itemConfig.label)}</label> <div class=\"infobar-item-control\"> <input type=\"number\" class=\"infobar-text-input summary-interval-input\" id=\"infobar-item-${panel_config.id}-${itemIdStr}\" data-panel-id=\"${panel_config.id}\" data-item-id=\"${itemIdStr}\" value=\"${escapeHtml(currentValue)}\" min=\"1\" style=\"width:80px;\" ${!panelIsEnabled || !itemIsEnabled ? 'disabled' : ''}> </div> </div>`;\n                        } else if (itemConfig.type === 'button') {\n                             return `<div class=\"infobar-item-row infobar-button-group\" style=\"justify-content: center;\"> <button id=\"infobar-${panel_config.id}-${itemConfig.id}-btn\" class=\"infobar-button-input\" data-panel-id=\"${panel_config.id}\" data-item-id=\"${itemConfig.id}\" style=\"flex-grow:0.5;\" ${!panelIsEnabled || !itemIsEnabled ? 'disabled' : ''}>${escapeHtml(itemConfig.buttonText)}</button> </div>`;\n                        } else if (itemConfig.type === 'display') { \n                            let displayValue = summaryData[itemIdStr] || itemConfig.defaultDisplayValue;\n                            if ((itemIdStr === 'lastBigSummaryTitle' || itemIdStr === 'lastSmallSummaryTitle') && displayValue && displayValue !== \"(无)\") {\n                                displayValue = displayValue.substring(0, 40) + (displayValue.length > 40 ? '...' : '');\n                            }\n                            return `<div class=\"infobar-item-row\"> <span class=\"infobar-item-label\">${escapeHtml(itemConfig.label)}:</span> <span class=\"infobar-display-value\" title=\"${escapeHtml(summaryData[itemIdStr] || '')}\">${escapeHtml(displayValue)}</span> </div>`;\n                        }\n                        return '';\n                    }).join('');\n                } else if (panel_config.isUtilityPanel && panel_config.id === 'backupManagement') { \n                    itemsHtml = `<div id=\"infobar-backup-list-container\" style=\"max-height: 300px; overflow-y: auto; border: 1px solid var(--infobar-border-color); border-radius: 4px; margin-bottom:10px;\"><p style=\"text-align:center; padding:10px;\">正在加载数据管理信息...</p> </div>`; \n                } else if (!panel_config.isUtilityPanel) { \n                    const panelSettings = currentSettings[panel_config.id]; \n                    let itemsToRender = panel_config.items; \n                    if (panelId === 'interactionDisplay' && panel_config.npcObjectStructure) { itemsToRender = panel_config.npcObjectStructure; }\n                    if (itemsToRender && Array.isArray(itemsToRender) && panelSettings) { \n                        itemsHtml = itemsToRender.map(item => { \n                            const itemIdStr = String(item.id); \n                            return `<div class=\"infobar-item-row infobar-toggle-row\"> <label for=\"infobar-item-${panel_config.id}-${itemIdStr}\" class=\"infobar-item-label\">${escapeHtml(item.label)}</label> <div class=\"infobar-item-control\"> <label class=\"infobar-switch\"> <input type=\"checkbox\" class=\"infobar-item-checkbox\" id=\"infobar-item-${panel_config.id}-${itemIdStr}\" data-panel-id=\"${panel_config.id}\" data-item-id=\"${itemIdStr}\" ${panelSettings.itemsEnabled?.[itemIdStr] ? 'checked' : ''} ${!panelSettings.enabled ? 'disabled' : ''}> <span class=\"infobar-slider round\"></span> </label> </div> </div>`; \n                        }).join(''); \n                    } \n                } \n                panelContentHtml += `<div class=\"infobar-tab-content ${firstPanel ? 'active' : ''}\" id=\"infobar-tab-${panel_config.id}\"><p class=\"infobar-panel-description\">${escapeHtml(panel_config.description)}</p>${!panel_config.isUtilityPanel && panel_config.id !== 'generalSettings' ? `<div class=\"infobar-item-row infobar-master-toggle\"><label for=\"infobar-master-${panel_config.id}\" class=\"infobar-item-label infobar-master-label\">启用 ${escapeHtml(panel_config.label)} (总开关)</label><div class=\"infobar-item-control\"><label class=\"infobar-switch\"><input type=\"checkbox\" class=\"infobar-master-checkbox\" id=\"infobar-master-${panel_config.id}\" data-panel-id=\"${panel_config.id}\" ${currentSettings[panel_config.id]?.enabled ? 'checked' : ''}><span class=\"infobar-slider round\"></span></label></div></div><h4 class=\"infobar-items-header\">详细信息点开关:</h4>` : ''}<div class=\"infobar-items-list ${!panel_config.isUtilityPanel && panel_config.id !== 'generalSettings' && !currentSettings[panel_config.id]?.enabled ? 'infobar-disabled-section' : ''}\" id=\"infobar-items-list-${panel_config.id}\">${itemsHtml}</div></div>`; \n                firstPanel = false; \n            } \n            if (panelTabsHtml === '') panelContentHtml = '<p>没有可配置的面板。请检查脚本的 INFO_CONFIG。</p>'; \n            const popupContent = `<div class=\"infobar-popup-header\"><h3>${BUTTON_TEXT}</h3><button id=\"infobar-close-${POPUP_ID}\" class=\"infobar-close-button\">&times;</button></div><div class=\"infobar-popup-body\"><div class=\"infobar-tabs-sidebar\">${panelTabsHtml}</div><div class=\"infobar-content-main\">${panelContentHtml}</div></div><div class=\"infobar-popup-footer\"><button id=\"infobar-save-settings\" class=\"infobar-save-button\"><i class=\"fa-solid fa-save\"></i> 保存设置</button><span id=\"infobar-save-feedback\" class=\"infobar-save-feedback\"></span></div>`; \n            createPopupElementUPM(popupContent); \n            initPopupEventsUPM(); \n            if ($('#infobar-tab-backupManagement', localParentDoc).is('.active')) updateBackupPanelList(); \n            if ($('#infobar-tab-generalSettings', localParentDoc).is('.active') || INFO_CONFIG.panels.generalSettings && Object.keys(INFO_CONFIG.panels)[0] === 'generalSettings' && firstPanel) { $('#infobar-theme-select', localParentDoc).val(currentTheme); } \n            applyTheme(currentTheme); \n        }, null, 'showSettingsPopupUPMInternal')(); \n    }\n    function createPopupElementUPM(contentHtml) { errorCatched(() => { const pD = window.parent.document; if(!pD) { console.error(\"[信息栏UPM ERROR] createPopupElementUPM: Parent document is null!\"); return;} $(`#${POPUP_ID}-overlay`, pD).remove(); $(`#${POPUP_ID}`, pD).remove(); const $o = $('<div></div>').attr('id', `${POPUP_ID}-overlay`).addClass('infobar-popup-overlay'); const $p = $('<div></div>').attr('id', POPUP_ID).addClass('infobar-popup').html(contentHtml); $('body', pD).addClass('infobar-body-no-scroll').append($o).append($p); }, null, 'createPopupElementUPM')(); }\n    function initPopupEventsUPM() { errorCatched(() => { const localParentDoc = window.parent.document; if(!localParentDoc) { console.error(\"[信息栏UPM ERROR] initPopupEventsUPM: Parent document is null!\"); return;} const namespace = `.infobarUPM.${POPUP_ID}`; $(localParentDoc).off(namespace); $(localParentDoc).on(`click${namespace}`, `#infobar-close-${POPUP_ID}, #${POPUP_ID}-overlay`, function(e) { if (e.target === this || $(e.target).attr('id') === `infobar-close-${POPUP_ID}`) { closePopupUPM(); } }); $(localParentDoc).on(`click${namespace}`, '.infobar-tab-button', function() { const tI = $(this).data('tab'); $('.infobar-tab-button', localParentDoc).removeClass('active'); $(this).addClass('active'); $('.infobar-tab-content', localParentDoc).removeClass('active'); $(`#infobar-tab-${tI}`, localParentDoc).addClass('active'); if (tI === 'backupManagement') updateBackupPanelList(); }); $(localParentDoc).on(`change${namespace}`, '.infobar-master-checkbox', function() { const pI = $(this).data('panel-id'); const iE = $(this).is(':checked'); if(currentSettings[pI]) currentSettings[pI].enabled = iE; $(`#infobar-items-list-${pI}`, localParentDoc).find('.infobar-item-checkbox, .summary-interval-input, #infobar-summary-manualSummaryTrigger-btn').prop('disabled', !iE).closest('.infobar-items-list').toggleClass('infobar-disabled-section', !iE);}); $(localParentDoc).on(`change${namespace}`, '.infobar-item-checkbox', function() { const pI = $(this).data('panel-id'); const iI = $(this).data('item-id'); if(currentSettings[pI]?.itemsEnabled) currentSettings[pI].itemsEnabled[iI] = $(this).is(':checked');}); $(localParentDoc).on(`change${namespace}`, '#infobar-default-collapsed-toggle', function() { infoBarDefaultCollapsed = $(this).is(':checked'); }); $(localParentDoc).on(`change${namespace}`, '#infobar-theme-select', function() { applyTheme($(this).val()); }); $(localParentDoc).on(`click${namespace}`, '#infobar-view-current-chatvar-btn', async function() { const data = await loadInfoBarDataFromChatVars(); if (data) { const jsonData = JSON.stringify(data, null, 2); if (typeof SillyTavern !== 'undefined' && SillyTavern.callGenericPopup && SillyTavern.POPUP_TYPE) { SillyTavern.callGenericPopup( `<div style=\"white-space:pre-wrap; word-break:break-all; max-height:70vh; overflow-y:auto; background:var(--SillyTavernTextBox背景, #222); color:var(--textCr, #ddd); padding:10px; border-radius:4px;\">${escapeHtml(jsonData)}</div>`, SillyTavern.POPUP_TYPE.TEXT, `当前角色信息栏数据 (聊天变量)` ); } else { alert(\"当前角色信息栏数据:\\n\" + jsonData); }} else { notifyUser(\"当前角色没有信息栏数据。\", \"info\"); }}); $(localParentDoc).on(`click${namespace}`, '#infobar-clear-current-char-data-btn', async function() { await clearCurrentCharInfoBarData(true); updateBackupPanelList(); }); \n        \n        $(localParentDoc).on(`change${namespace}`, '.summary-interval-input', async function() {\n            const panelId = $(this).data('panel-id');\n            const itemId = $(this).data('item-id');\n            let value = parseInt($(this).val(), 10);\n            const isBig = itemId === 'bigSummaryInterval';\n            if (isNaN(value) || value < 1) value = isBig ? 30 : 10;\n            $(this).val(value);\n            \n            let data = await loadInfoBarDataFromChatVars() || {};\n            data.summary = data.summary || { aiTurnCount: 0, lastBigSummaryMsgCount: 0, lastSmallSummaryMsgCount: 0, bigSummaryInterval: 30, smallSummaryInterval: 10, enableBigSummary: true, enableSmallSummary: true, lastBigSummaryTitle: \"\", lastSmallSummaryTitle: \"\" };\n            data.summary[itemId] = value;\n            await saveInfoBarDataToChatVars(data);\n            notifyUser(`${ isBig ? '大' : '小' }总结间隔已更新为 ${value}`, 'success', 2000);\n        });\n        $(localParentDoc).on(`click${namespace}`, '#infobar-summary-manualSummaryTrigger-btn', async function() { // Corrected selector\n            await triggerSummary('small', true); \n        });\n\n        $(localParentDoc).on(`click${namespace}`, '#infobar-save-settings', function() { const $f=$(`#infobar-save-feedback`,localParentDoc);const $b=$(this);const o=$b.html();$b.prop('disabled',true).html('<i class=\"fa-solid fa-spinner fa-spin\"></i> 保存中...');$f.text('').removeClass('success error'); setTimeout(()=>{ if(saveSettings()){$f.text('设置已保存!').addClass('success');notifyUser('信息栏设置已保存!','success');}else{$f.text('保存失败!').addClass('error');notifyUser('信息栏设置保存失败!','error');} $b.prop('disabled',false).html(o); setTimeout(()=>$f.text(''),3000); },300); }); \n    }, null, 'initPopupEventsUPM')(); }\n    function closePopupUPM() { errorCatched(() => { const pD = window.parent.document; if(pD) $('body', pD).removeClass('infobar-body-no-scroll'); $(`#${POPUP_ID}`, pD).remove(); $(`#${POPUP_ID}-overlay`, pD).remove(); }, null, 'closePopupUPM')(); }\n    function addGlobalStylesUPM() { errorCatched(() => { const pD = window.parent.document; if(!pD) return; if ($('#infobar-upm-styles', pD).length > 0) return; const styles = ` .infobar-body-no-scroll { overflow: hidden !important; } .infobar-popup-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.6); z-index: 10000; backdrop-filter: blur(3px); } .infobar-popup { position: fixed; z-index: 10001; display: flex; flex-direction: column; font-family: 'Segoe UI', Roboto, sans-serif; font-size: 14px; top: 0; left: 0; width: 100%; height: 100dvh; border-radius:0; box-shadow:none; } .infobar-popup.light { background-color: #fdfdfd; color: #333; --infobar-border-color: #e0e0e0; --infobar-bg-subtle: #f7f7f7; --infobar-text-muted: #555; --infobar-accent: #007bff; --infobar-switch-bg: #ccc; --infobar-switch-knob: white; --infobar-switch-bg-checked: #007bff;} .infobar-popup.dark { background-color: #2b2d31; color: #ccc; --infobar-border-color: #3a3d42; --infobar-bg-subtle: #26282c; --infobar-text-muted: #aaa; --infobar-accent: #58a6ff; --infobar-switch-bg: #555; --infobar-switch-knob: #333; --infobar-switch-bg-checked: #58a6ff;} .infobar-popup.cyberpunk { background-color: #0d0221; color: #95f0f5; --infobar-border-color: #4f208c; --infobar-bg-subtle: #1a0a3d; --infobar-text-muted: #7c3eff; --infobar-accent: #f0f57a; --infobar-switch-bg: #4f208c; --infobar-switch-knob: #1a0a3d; --infobar-switch-bg-checked: #f0f57a; font-family: 'Orbitron', monospace; } .infobar-popup.steampunk { background-color: #e6d8b8; color: #5a3e2b; --infobar-border-color: #a07e5f; --infobar-bg-subtle: #d8c0a0; --infobar-text-muted: #8B4513; --infobar-accent: #7a5032; --infobar-switch-bg: #a07e5f; --infobar-switch-knob: #d8c0a0; --infobar-switch-bg-checked: #7a5032; font-family: 'IM Fell DW Pica', serif; } .infobar-popup.minimalist-modern { background-color: #ffffff; color: #212121; --infobar-border-color: #eeeeee; --infobar-bg-subtle: #f9f9f9; --infobar-text-muted: #757575; --infobar-accent: #00796b; --infobar-switch-bg: #bdbdbd; --infobar-switch-knob: #f5f5f5; --infobar-switch-bg-checked: #00796b; } .infobar-popup.parchment-magical { background-color: #fdf5e6; color: #5d4037; --infobar-border-color: #d2b48c; --infobar-bg-subtle: #f7efdd; --infobar-text-muted: #a1887f; --infobar-accent: #8d6e63; --infobar-switch-bg: #d2b48c; --infobar-switch-knob: #f7efdd; --infobar-switch-bg-checked: #8d6e63; font-family: 'EB Garamond', serif; } @media (min-width: 768px) { .infobar-popup { top: 50%; left: 50%; transform: translate(-50%, -50%); width: 800px; max-width: 90vw; height: auto; max-height: 90vh; border-radius: 8px; box-shadow: 0 6px 18px rgba(0,0,0,0.2); font-size: 15px; } } .infobar-popup-header { padding: 12px 18px; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid var(--infobar-border-color); user-select:none; flex-shrink:0; } .infobar-popup-header h3 { margin: 0; font-size: 1.1em; font-weight: 500; } .infobar-popup-header .infobar-close-button { background:none; border:none; font-size: 1.6em; cursor:pointer; padding:0 5px; line-height:1; opacity:0.7; color: var(--infobar-text-muted); } .infobar-popup-header .infobar-close-button:hover { opacity:1; } .infobar-popup-body { display: flex; flex-grow: 1; overflow: hidden; flex-direction: column; } @media (min-width: 768px) { .infobar-popup-body { flex-direction: row; } } .infobar-tabs-sidebar { display: flex; flex-direction: row; overflow-x: auto; white-space: nowrap; padding: 8px 0; border-bottom: 1px solid var(--infobar-border-color); background-color: var(--infobar-bg-subtle); flex-shrink:0; scrollbar-width: thin; } .infobar-tabs-sidebar::-webkit-scrollbar { height: 3px; } .infobar-tabs-sidebar::-webkit-scrollbar-thumb { background-color: #aaa; border-radius: 3px; } .infobar-popup.dark .infobar-tabs-sidebar::-webkit-scrollbar-thumb { background-color: #555; } @media (min-width: 768px) { .infobar-tabs-sidebar { flex-direction: column; width: 220px; min-width:200px; padding: 10px 0; border-bottom: none; border-right: 1px solid var(--infobar-border-color); overflow-x:hidden; overflow-y:auto; } } .infobar-tab-button { background: none; border: none; text-align: left; padding: 10px 15px; font-size: 0.9em; cursor: pointer; border-bottom: 3px solid transparent; transition: all 0.2s ease; display: inline-flex; align-items: center; gap: 8px; flex-shrink:0; color: var(--infobar-text-muted); } .infobar-tab-button .infobar-tab-label { white-space: nowrap; } @media (min-width: 768px) { .infobar-tab-button { display: flex; width: 100%; padding: 12px 20px; border-left: 3px solid transparent; border-bottom: none; } } .infobar-popup.light .infobar-tab-button:hover { background-color: #e9e9e9; } .infobar-popup.dark .infobar-tab-button:hover { background-color: #33363c; } .infobar-popup.cyberpunk .infobar-tab-button:hover { background-color: #2c1a52; } .infobar-popup.steampunk .infobar-tab-button:hover { background-color: #c9ae8d; } .infobar-tab-button.active { color: var(--infobar-accent); border-bottom-color: var(--infobar-accent); } .infobar-popup.light .infobar-tab-button.active { background-color: #fff; } .infobar-popup.dark .infobar-tab-button.active { background-color: #2b2d31;} .infobar-popup.cyberpunk .infobar-tab-button.active { background-color: #0d0221; border-bottom-color: var(--infobar-accent); } .infobar-popup.steampunk .infobar-tab-button.active { background-color: #e6d8b8; border-bottom-color: var(--infobar-accent); } @media (min-width: 768px) { .infobar-tab-button.active { border-left-color: var(--infobar-accent); border-bottom-color:transparent; } } .infobar-tab-button i { width: 1.2em; text-align: center; opacity: 0.9; } .infobar-content-main { flex-grow: 1; padding: 15px; overflow-y: auto; scrollbar-width:thin; } .infobar-content-main::-webkit-scrollbar { width: 6px; } .infobar-content-main::-webkit-scrollbar-thumb { background-color: #ccc; border-radius: 3px; } .infobar-popup.dark .infobar-content-main::-webkit-scrollbar-thumb { background-color: #555; } .infobar-tab-content { display: none; } .infobar-tab-content.active { display: block; animation: infobarFadeIn 0.2s ease-out; } @keyframes infobarFadeIn { from { opacity: 0; } to { opacity: 1; } } .infobar-panel-description { font-size: 0.9em; margin-bottom: 15px; padding: 10px; border-radius: 5px; line-height:1.5; border: 1px solid var(--infobar-border-color); background-color: var(--infobar-bg-subtle);} .infobar-items-header { font-size: 1em; font-weight:500; margin-top: 20px; margin-bottom: 10px; padding-bottom:6px; border-bottom: 1px dashed var(--infobar-border-color); } .infobar-items-list { max-height: calc(100dvh - 380px); overflow-y: auto; scrollbar-width:thin; padding-right: 5px; padding-bottom: 1px; } .infobar-items-list.infobar-disabled-section { opacity: 0.6; pointer-events: none; } .infobar-popup.light .infobar-items-list.infobar-disabled-section { background-color: rgba(0,0,0,0.02); } .infobar-popup.dark .infobar-items-list.infobar-disabled-section { background-color: rgba(255,255,255,0.02); } .infobar-items-list::-webkit-scrollbar { width: 5px; } .infobar-items-list::-webkit-scrollbar-thumb { background-color: #bbb; border-radius: 3px; } .infobar-popup.dark .infobar-items-list::-webkit-scrollbar-thumb { background-color: #444; } @media (min-width: 768px) { .infobar-items-list { max-height: 350px; } } .infobar-item-row { display: flex; justify-content: space-between; align-items: center; padding: 8px 2px; border-bottom: 1px solid; min-height: 34px; } .infobar-item-row.infobar-button-group { justify-content: flex-start; gap: 10px; } .infobar-popup.light .infobar-item-row { border-bottom-color: #f0f0f0; } .infobar-popup.dark .infobar-item-row { border-bottom-color: #36393f; } .infobar-item-row:last-child { border-bottom: none; } .infobar-item-label { flex-grow: 1; padding-right: 10px; font-size:0.95em; } .infobar-item-label small { display:block; font-size:0.85em; opacity:0.7; margin-top:2px; color: var(--infobar-text-muted); } .infobar-item-control { flex-shrink: 0; } .infobar-toggle-row .infobar-item-label { cursor:pointer; } .infobar-hidden-dependency { display: none !important; } .infobar-disabled-item { opacity:0.5; pointer-events:none; } .infobar-text-input, .infobar-select-input, .infobar-textarea-input { padding: 8px 10px; border-radius: 4px; font-size: 0.9em; border: 1px solid; width: 100%; box-sizing:border-box; } .infobar-select-input { min-width: 150px; } .infobar-textarea-input { min-height: 80px; resize: vertical; font-family: inherit; } .infobar-popup.light .infobar-text-input, .infobar-popup.light .infobar-select-input, .infobar-popup.light .infobar-textarea-input { border-color: #ccc; background-color: #fff; color: #333; } .infobar-popup.dark .infobar-text-input, .infobar-popup.dark .infobar-select-input, .infobar-popup.dark .infobar-textarea-input { border-color: #555; background-color: #3a3d42; color: #ddd; } .infobar-textarea-row .infobar-item-label { align-self: flex-start; padding-top: 8px; } .infobar-radio-group { display: flex; flex-direction: column; gap: 6px; } .infobar-radio-label { display: flex; align-items: center; cursor: pointer; font-size: 0.9em;} .infobar-radio-label input[type=\"radio\"] { margin-right: 8px; transform: scale(0.9); } .infobar-button-input { padding: 8px 12px; font-size: 0.9em; border-radius: 4px; cursor:pointer; border: 1px solid; flex-grow: 1; text-align:center;} .infobar-popup.light .infobar-button-input { background-color: #e9ecef; border-color: #ced4da; color: #495057; } .infobar-popup.dark .infobar-button-input { background-color: #4a4e56; border-color: #5e636e; color: #dee2e6; } .infobar-danger-button { background-color: var(--Danger, #dc3545) !important; color: white !important; border-color: var(--Danger, #dc3545) !important; } .infobar-danger-button:hover { filter: brightness(0.9); } .infobar-popup-footer { padding: 12px 18px; text-align: right; border-top: 1px solid var(--infobar-border-color); user-select:none; flex-shrink:0; background-color: var(--infobar-bg-subtle); } .infobar-save-button { background-color: #28a745; color: white; border:none; padding: 9px 18px; font-size: 0.95em; border-radius: 5px; cursor:pointer; } .infobar-popup.cyberpunk .infobar-save-button { background-color: #f0f57a; color: #0d0221; } .infobar-popup.steampunk .infobar-save-button { background-color: #7a5032; color: #e6d8b8; } .infobar-save-button:hover { background-color: #218838; } .infobar-popup.cyberpunk .infobar-save-button:hover { background-color: #d4d856; } .infobar-popup.steampunk .infobar-save-button:hover { background-color: #603e24; } .infobar-save-button:disabled { background-color: #777; } .infobar-save-button i { margin-right: 7px; } .infobar-save-feedback { margin-left: 15px; font-style: italic; font-size: 0.9em; } .infobar-save-feedback.success { color: #28a745; } .infobar-popup.dark .infobar-save-feedback.success { color: #33c06d; } .infobar-save-feedback.error { color: #dc3545; } .infobar-popup.dark .infobar-save-feedback.error { color: #ff6b81; } .infobar-switch { position: relative; display: inline-block; width: 42px; height: 22px; vertical-align: middle; } .infobar-switch input { opacity: 0; width: 0; height: 0; } .infobar-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: var(--infobar-switch-bg); transition: .3s; } .infobar-slider:before { position: absolute; content: \"\"; height: 16px; width: 16px; left: 3px; bottom: 3px; background-color: var(--infobar-switch-knob); transition: .3s; box-shadow: 0 1px 2px rgba(0,0,0,0.3); } .infobar-switch input:checked + .infobar-slider { background-color: var(--infobar-switch-bg-checked); } .infobar-switch input:focus + .infobar-slider { box-shadow: 0 0 1px var(--infobar-accent); } .infobar-switch input:checked + .infobar-slider:before { transform: translateX(20px); } .infobar-popup.dark .infobar-switch input:checked + .infobar-slider:before { background-color: #fff; } .infobar-slider.round { border-radius: 22px; } .infobar-slider.round:before { border-radius: 50%; } .infobar-backup-items-ul { list-style: none; padding: 0; margin: 0; } .infobar-backup-item-li { display: flex; justify-content: space-between; align-items: center; padding: 8px 10px; border-bottom: 1px solid var(--infobar-border-color); font-size: 0.9em;} .infobar-backup-item-li:last-child { border-bottom: none; } .infobar-backup-item-li .backup-char-name { flex-grow: 1; font-weight: 500; } .infobar-backup-item-li .backup-time { font-style: italic; color: var(--infobar-text-muted); margin-left: 10px; margin-right: 10px; white-space:nowrap; } .infobar-backup-item-li .backup-actions { display: flex; gap: 5px; } .infobar-small-button { padding: 3px 8px; font-size: 0.85em !important; flex-grow:0 !important; }\n            `;\n            const bodyScrollLockStyle = `body.infobar-body-no-scroll { overflow: hidden !important; }`;\n            if ($('#infobar-body-scroll-lock-style', pD.head).length === 0) { $('head', pD).append(`<style id=\"infobar-body-scroll-lock-style\">${bodyScrollLockStyle}</style>`);}\n            $('head', pD).append(`<style id=\"infobar-upm-styles\">${styles}</style>`);\n        }, null, 'addGlobalStylesUPM')(); }\n    \n    // --- \"Backend\" Display Logic ---\n    function parseAIDataBlock(messageText) { \n        return errorCatched(() => { \n            const thinkBlockMatch = messageText.match(AI_THINK_PROCESS_REGEX);\n            if (thinkBlockMatch && thinkBlockMatch[1]) {\n                messageText = messageText.replace(AI_THINK_PROCESS_REGEX, '').trim();\n            }\n\n            const dataMatch = messageText.match(AI_DATA_BLOCK_REGEX); \n            if (!dataMatch || !dataMatch[1]) {\n                return null; \n            }\n            const dataString = dataMatch[1].trim(); \n            const parsedData = {}; \n            \n            // Try parsing as a single JSON object first (less strict, but sometimes AI does this)\n            if (dataString.startsWith('{') && dataString.endsWith('}')) {\n                try {\n                    const jsonData = JSON.parse(dataString);\n                    for (const key in jsonData) {\n                        if (Object.prototype.hasOwnProperty.call(jsonData, key)) {\n                            if (typeof jsonData[key] === 'object' && jsonData[key] !== null) {\n                                parsedData[key] = JSON.stringify(jsonData[key]); \n                            } else {\n                                parsedData[key] = String(jsonData[key]);\n                            }\n                        }\n                    }\n                    if (Object.keys(parsedData).length > 0) return parsedData;\n                } catch (e) { /* Fall through to line-by-line if single JSON parse fails */ }\n            }\n            \n            const lines = dataString.split(/\\r?\\n/).map(l => l.trim()).filter(l => l && !l.startsWith('#') && l.includes(':')); \n            if (lines.length === 0) return null;\n\n            lines.forEach(line => { \n                const itemMatch = line.match(/^([a-zA-Z0-9]+(?:[1-9]\\d*)?(?:\\.[a-zA-Z0-9_]+)+):\\s*([\\s\\S]*?)$/); \n                if (itemMatch && itemMatch[1] && itemMatch[2] !== undefined) { \n                    const key = itemMatch[1].trim(); \n                    const value = itemMatch[2].trim(); \n                    parsedData[key] = value; \n                } \n            }); \n            return Object.keys(parsedData).length > 0 ? parsedData : null; \n        }, null, 'parseAIDataBlock')(); \n    }\n    function parseComplexString(str) { return errorCatched(() => { const data = {}; if (typeof str !== 'string') return data; str.split('#').forEach(part => { const kv = part.split('='); if (kv.length === 2) data[kv[0].trim()] = kv[1].trim(); }); return data; }, null, 'parseComplexString')(); }\n    \n    async function triggerSummary(summaryType = 'small', manualTrigger = false) { \n        return errorCatched(async () => {\n            if (!SillyTavern.getContext) {\n                notifyUser(\"SillyTavern API (getContext) not available for summary.\", \"error\");\n                return;\n            }\n            \n            let data = await loadInfoBarDataFromChatVars() || {};\n            data.summary = data.summary || { aiTurnCount: 0, lastBigSummaryMsgCount: 0, lastSmallSummaryMsgCount: 0, bigSummaryInterval: 30, smallSummaryInterval: 10, enableBigSummary: true, enableSmallSummary: true, lastBigSummaryTitle: \"\", lastSmallSummaryTitle: \"\" };\n            const currentAiMsgCount = data.summary.aiTurnCount || 0;\n\n            let chatVars = await getVariables({ type: 'chat' }) || {};\n            if (summaryType === 'big') {\n                 chatVars['infobar_upm_character_data_v1.summary.needsBigSummary'] = true;\n                 delete chatVars['infobar_upm_character_data_v1.summary.needsSmallSummary']; \n            } else { \n                 chatVars['infobar_upm_character_data_v1.summary.needsSmallSummary'] = true;\n                 delete chatVars['infobar_upm_character_data_v1.summary.needsBigSummary'];\n            }\n            \n            chatVars['infobar_upm_character_data_v1.summary.lastTriggeredType'] = summaryType; \n            chatVars['infobar_upm_character_data_v1.summary.summaryTriggerMsgCount'] = currentAiMsgCount; \n\n            await replaceVariables(chatVars, { type: 'chat' });\n            \n            if (manualTrigger) {\n                notifyUser(`手动${summaryType}总结请求已设置。\\n请现在发送一条简短消息 (例如 '.') 以触发AI生成总结。`, 'info', 10000); \n            } else {\n                notifyUser(`已设置AI进行 ${summaryType} 总结请求，将在下次AI回复时生成。`, 'info');\n            }\n        }, null, 'triggerSummary')();\n    }\n\n\n    function renderSingleNPCDetailsHTML(npcData, panelConfig, panelSettings) {\n        let detailHtml = '';\n        if (!npcData || !panelConfig || !panelSettings || !npcData.id || npcData.id === \"(数据获取失败)\") { // Added check for invalid ID\n            return `<div style=\"text-align:center; padding: 10px 0; color: var(--infobar-text-muted);\">(NPC数据无效或未选定)</div>`;\n        }\n        \n        panelConfig.npcObjectStructure.forEach(subItemConf => {\n            const subItemEnabled = panelSettings.itemsEnabled?.[subItemConf.id];\n            let subItemValue = npcData[subItemConf.id]; \n            \n            if (subItemEnabled) {\n                let valueIsDefault = (subItemValue === subItemConf.defaultDisplayValue);\n                let valueIsGenericAIPlaceholder = AI_PLACEHOLDERS.includes(String(subItemValue));\n\n                if (subItemValue === undefined || (valueIsGenericAIPlaceholder && !valueIsDefault)) {\n                    subItemValue = subItemConf.defaultDisplayValue || \"(未提供)\";\n                }\n                \n                const finalValueIsGenericPlaceholderToHide = AI_PLACEHOLDERS.includes(String(subItemValue)) && String(subItemValue) !== subItemConf.defaultDisplayValue;\n\n                if (!finalValueIsGenericPlaceholderToHide || subItemValue === subItemConf.defaultDisplayValue) {\n                    let displayVal = escapeHtml(String(subItemValue));\n                    if (subItemConf.isComplex && subItemConf.complexType === 'stringListSimple' && typeof subItemValue === 'string') {\n                        try {\n                            const parsedList = JSON.parse(subItemValue);\n                            if (Array.isArray(parsedList) && parsedList.length > 0) {\n                                displayVal = '<ul>' + parsedList.map(s => `<li>${escapeHtml(String(s))}</li>`).join('') + '</ul>';\n                            } else if (Array.isArray(parsedList) && parsedList.length === 0) {\n                                displayVal = `<span>(列表为空)</span>`;\n                            }\n                        } catch (e) { /* use raw value if parse fails but not a placeholder */ }\n                    } else if (subItemConf.isComplex && subItemConf.complexType === 'jsonString' && typeof subItemValue === 'string') {\n                         try {\n                            const parsedJson = JSON.parse(subItemValue);\n                            displayVal = `<pre style=\"white-space: pre-wrap; word-break: break-all; font-size:0.9em; max-height: 100px; overflow-y:auto;\">${escapeHtml(JSON.stringify(parsedJson, null, 2))}</pre>`;\n                        } catch (e) { /* use raw value */ }\n                    }\n                     detailHtml += `<div class=\"infobar-display-item npc-detail-item\"><span class=\"infobar-display-label npc-detail-label\">${escapeHtml(subItemConf.label)}:</span><span class=\"infobar-display-value npc-detail-value\">${displayVal}</span></div>`;\n                }\n            }\n        });\n        return detailHtml || `<div style=\"text-align:center; padding: 10px 0; color: var(--infobar-text-muted);\">(无启用的详细信息或数据)</div>`;\n    }\n\n\n    function renderInfoBarHTML(dataToRender) { \n        return errorCatched(() => { \n            if (!dataToRender || Object.keys(dataToRender).length === 0) {\n                return ''; \n            }\n            \n            // Define theme object for consistent styling access throughout this function\n            let effectiveSystemTheme = currentTheme;\n            if (currentTheme === 'auto') {\n                effectiveSystemTheme = ($('body', window.parent.document).hasClass('dark') || $('body', window.parent.document).hasClass('dark_mode')) ? 'dark' : 'light';\n            }\n            let uiTheme = { itemBorder: effectiveSystemTheme === 'dark' ? 'rgba(255,255,255,0.08)' : '#e2e8f0' }; // Simplified, getRenderedInfoBarStyle provides full\n\n\n            let topTimeHtml = ''; \n             if (currentSettings?.worldTime?.enabled) {\n                const date = dataToRender['worldTime.date'] || INFO_CONFIG.panels.worldTime?.items?.find(i => i.id === 'date')?.defaultDisplayValue || \"(日期未知)\";\n                const time = dataToRender['worldTime.time'] || INFO_CONFIG.panels.worldTime?.items?.find(i => i.id === 'time')?.defaultDisplayValue || \"(时间未知)\";\n                const weather = dataToRender['worldTime.weather'] || INFO_CONFIG.panels.worldTime?.items?.find(i => i.id === 'weather')?.defaultDisplayValue || \"(天气未知)\";\n                const hasActualTimeData = !AI_PLACEHOLDERS.some(p => String(date).toLowerCase().includes(p.toLowerCase())) || !AI_PLACEHOLDERS.some(p => String(time).toLowerCase().includes(p.toLowerCase())) || !AI_PLACEHOLDERS.some(p => String(weather).toLowerCase().includes(p.toLowerCase()));\n                if (hasActualTimeData) {\n                    topTimeHtml = `<div class=\"${TOP_TIME_DISPLAY_CLASS}\"><span><i class=\"fa-solid fa-calendar-days\"></i> ${escapeHtml(date)}</span><span><i class=\"fa-solid fa-clock\"></i> ${escapeHtml(time)}</span><span><i class=\"fa-solid fa-cloud-sun\"></i> ${escapeHtml(weather)}</span></div>`;\n                }\n            }\n            \n            let infoBarHtml = `<div class=\"${RENDERED_INFO_BAR_CLASS} theme-${effectiveSystemTheme}\">`; \n            let hasAnyContent = false; \n            \n            for (const panelId in INFO_CONFIG.panels) { \n                const panelConfig = INFO_CONFIG.panels[panelId]; \n                if (!panelConfig || panelConfig.isUtilityPanel || panelId === 'worldTime') continue; \n                const panelSettings = currentSettings[panelId]; \n                if (!panelSettings || !panelSettings.enabled) continue; \n                \n                let panelItemsHtml = ''; \n                let hasPanelItemsCurrentSection = false;\n                \n                if (panelId === 'interactionDisplay') {\n                    const trackedNPCs = dataToRender.trackedNPCs || {};\n                    const displayableNPCs = Object.values(trackedNPCs).filter(npc => npc && npc.id && npc.name && npc.id !== \"(数据获取失败)\");\n\n                    if (displayableNPCs.length > 0) {\n                        let selectorHtml = `<div class=\"infobar-npc-selector-container\"><label for=\"${NPC_SELECTOR_ID}\" style=\"margin-right:5px;font-weight:500;\">查看对象:</label><select id=\"${NPC_SELECTOR_ID}\" class=\"infobar-select-input\" style=\"flex-grow:1; max-width: 200px;\">`;\n                        \n                        let npcIdToSelect = dataToRender.userSelectedNPCIdForDisplay || dataToRender.lastInteractedNPCId;\n                        if (!npcIdToSelect || !trackedNPCs[npcIdToSelect] || trackedNPCs[npcIdToSelect].id === \"(数据获取失败)\") { \n                            npcIdToSelect = displayableNPCs.length > 0 ? displayableNPCs[0].id : null; \n                        }\n                        \n                        displayableNPCs.sort((a,b) => String(a.name || \"\").localeCompare(String(b.name || \"\"))).forEach(npc => {\n                            selectorHtml += `<option value=\"${escapeHtml(npc.id)}\" ${npc.id === npcIdToSelect ? 'selected' : ''}>${escapeHtml(String(npc.name || npc.id ))}</option>`; \n                        });\n                        selectorHtml += `</select></div>`;\n                        panelItemsHtml += selectorHtml;\n                    }\n                    \n                    panelItemsHtml += `<div id=\"${NPC_DETAIL_CONTAINER_ID}\" class=\"npc-details-wrapper\">`;\n                    const npcIdForDisplay = dataToRender.userSelectedNPCIdForDisplay || dataToRender.lastInteractedNPCId || (displayableNPCs.length > 0 ? displayableNPCs[0].id : null);\n                    if (npcIdForDisplay && trackedNPCs[npcIdForDisplay] && trackedNPCs[npcIdForDisplay].id !== \"(数据获取失败)\") {\n                        panelItemsHtml += renderSingleNPCDetailsHTML(trackedNPCs[npcIdForDisplay], panelConfig, panelSettings);\n                    } else if (displayableNPCs.length === 0) { \n                         panelItemsHtml += `<div style=\"text-align:center; padding: 10px 0; color: var(--infobar-text-muted);\">(无已记录的交互对象)</div>`;\n                    } else if ($(`#${NPC_SELECTOR_ID}`).length > 0 && $(`#${NPC_SELECTOR_ID} option`).length > 0) {\n                         panelItemsHtml += `<div style=\"text-align:center; padding: 10px 0; color: var(--infobar-text-muted);\">(请从上方选择对象)</div>`;\n                    } else { \n                         panelItemsHtml += `<div style=\"text-align:center; padding: 10px 0; color: var(--infobar-text-muted);\">(无交互对象信息)</div>`;\n                    }\n                    panelItemsHtml += `</div>`;\n                    if (panelItemsHtml.trim() !== `<div id=\"${NPC_DETAIL_CONTAINER_ID}\" class=\"npc-details-wrapper\"></div>`) { \n                        hasPanelItemsCurrentSection = true;\n                    }\n\n                } else if (panelId === 'summary') {\n                    // ... (summary rendering, as before, should be fine if sumData is correct) ...\n                        const summaryData = dataToRender.summary || {};\n                        const currentAiTurn = summaryData.aiTurnCount || 0;\n                        let summaryStatusHtml = \"\";\n                        const sumSettingsItems = currentSettings.summary?.itemsEnabled || {};\n\n                        if (sumSettingsItems.enableSmallSummary && summaryData.smallSummaryInterval > 0) {\n                            const interval = parseInt(summaryData.smallSummaryInterval, 10) || 10;\n                            const nextSmall = (summaryData.lastSmallSummaryMsgCount || 0) + interval;\n                            const toNextSmall = nextSmall - currentAiTurn;\n                            summaryStatusHtml += `<div class=\"infobar-display-item\"><span class=\"infobar-display-label\">小总结:</span><span class=\"infobar-display-value\">${toNextSmall > 0 ? `约 ${toNextSmall} AI回复后` : '即将/已触发'}</span></div>`;\n                        }\n                         if (sumSettingsItems.enableBigSummary && summaryData.bigSummaryInterval > 0) {\n                             const interval = parseInt(summaryData.bigSummaryInterval, 10) || 30;\n                            const nextBig = (summaryData.lastBigSummaryMsgCount || 0) + interval;\n                            const toNextBig = nextBig - currentAiTurn;\n                            summaryStatusHtml += `<div class=\"infobar-display-item\"><span class=\"infobar-display-label\">大总结:</span><span class=\"infobar-display-value\">${toNextBig > 0 ? `约 ${toNextBig} AI回复后` : '即将/已触发'}</span></div>`;\n                        }\n                        if(sumSettingsItems.lastSmallSummaryTitle && summaryData.lastSmallSummaryTitle) {\n                            summaryStatusHtml += `<div class=\"infobar-display-item\"><span class=\"infobar-display-label\">上次小总结:</span><span class=\"infobar-display-value\" title=\"${escapeHtml(summaryData.lastSmallSummaryTitle)}\">${escapeHtml(summaryData.lastSmallSummaryTitle.substring(0,30))}${summaryData.lastSmallSummaryTitle.length > 30 ? '...' : ''}</span></div>`;\n                        }\n                         if(sumSettingsItems.lastBigSummaryTitle && summaryData.lastBigSummaryTitle) {\n                            summaryStatusHtml += `<div class=\"infobar-display-item\"><span class=\"infobar-display-label\">上次大总结:</span><span class=\"infobar-display-value\" title=\"${escapeHtml(summaryData.lastBigSummaryTitle)}\">${escapeHtml(summaryData.lastBigSummaryTitle.substring(0,30))}${summaryData.lastBigSummaryTitle.length > 30 ? '...' : ''}</span></div>`;\n                        }\n                        panelItemsHtml = summaryStatusHtml || `<div class=\"infobar-display-item\"><span class=\"infobar-display-label\">总结:</span><span class=\"infobar-display-value\">(未启用或无状态)</span></div>`;\n                        if (panelItemsHtml && panelItemsHtml.trim() !== `<div class=\"infobar-display-item\"><span class=\"infobar-display-label\">总结:</span><span class=\"infobar-display-value\">(未启用或无状态)</span></div>`) {\n                             hasPanelItemsCurrentSection = true;\n                        }\n                }\n                else if (panelConfig.isListPanel) { \n                    let itemsFoundForList = 0;\n                    const listContainerClass = `${panelId}-list-container`; \n                    let listContentHtml = \"\";\n                    const listKey = `${panelId}.list`;\n                    let listDataArray = [];\n                    const rawListData = dataToRender[listKey];\n\n                    if (rawListData) {\n                        if (typeof rawListData === 'string') {\n                            const trimmedList = rawListData.trim();\n                            if (trimmedList === '[]' || trimmedList === '') { listDataArray = []; }\n                            else if (trimmedList === '[') { listDataArray = []; }\n                            else { \n                                try { \n                                    const parsedList = JSON.parse(trimmedList); \n                                    if (Array.isArray(parsedList)) listDataArray = parsedList; \n                                } catch (e) { /* console.warn for parse fail */ } \n                            }\n                        } else if (Array.isArray(rawListData)) { listDataArray = rawListData; }\n                    }\n\n                    if (listDataArray.length > 0) {\n                        listDataArray.forEach(item => {\n                            if (panelId === 'affectionPanel' && item.name && item.value !== undefined) { /* ... render affection ... */ itemsFoundForList++; }\n                            else if (panelId === 'warehousePanel' && item.name && item.quantity !== undefined && !AI_PLACEHOLDERS.some(p => String(item.name).includes(p))) { /* ... render warehouse ... */ itemsFoundForList++; }\n                        });\n                    }\n                    if (itemsFoundForList > 0) {\n                        panelItemsHtml = `<div class=\"${listContainerClass} complex-data\">${listContentHtml}</div>`;\n                        hasPanelItemsCurrentSection = true;\n                    } else {\n                         panelItemsHtml = `<div class=\"${listContainerClass} complex-data\"><span style=\"color: var(--infobar-text-muted); font-style: italic;\">(列表为空或无有效条目)</span></div>`;\n                         hasPanelItemsCurrentSection = true;\n                    }\n                } else if (panelConfig.items && Array.isArray(panelConfig.items)) { \n                    let itemsRenderedForThisPanel = 0;\n                    panelConfig.items.forEach(itemConfig => { \n                        if (panelSettings.itemsEnabled[itemConfig.id]) { \n                            const dataKey = `${panelId}.${itemConfig.id}`; \n                            let rawValue = dataToRender[dataKey]; \n                            \n                            let valueIsPlaceholder = false;\n                            if (rawValue !== undefined && rawValue !== null) {\n                                valueIsPlaceholder = AI_PLACEHOLDERS.some(p => {\n                                    const placeholderLower = p.toLowerCase();\n                                    const rawValueLower = String(rawValue).toLowerCase();\n                                    return placeholderLower.startsWith('(') ? rawValueLower.includes(placeholderLower) : rawValueLower === placeholderLower;\n                                });\n                            }\n                            if (rawValue === undefined || (rawValue === null && itemConfig.defaultDisplayValue !== null) || (valueIsPlaceholder && rawValue !== itemConfig.defaultDisplayValue) ) {\n                                rawValue = itemConfig.defaultDisplayValue || \"(未提供)\";\n                                if (itemConfig.id !== 'name' && itemConfig.id !=='currentLocation' ) { \n                                     valueIsPlaceholder = AI_PLACEHOLDERS.some(p => String(rawValue).toLowerCase().includes(p.toLowerCase()));\n                                } else { valueIsPlaceholder = false; }\n                            }\n                            \n                            let shouldRenderItem = true;\n                            if ( (panelId === 'focusAffectionNPC' && itemConfig.id !== 'name' && dataToRender[`${panelId}.name`] === INFO_CONFIG.panels.focusAffectionNPC.items.find(i=>i.id==='name').defaultDisplayValue) ) {\n                                if (rawValue === itemConfig.defaultDisplayValue || valueIsPlaceholder) { shouldRenderItem = false; }\n                            }\n                            \n                            if (itemConfig.isComplex) {\n                                const isDefaultEmptyJson = (itemConfig.defaultDisplayValue === \"[]\" && rawValue === \"[]\") ||\n                                                        (itemConfig.defaultDisplayValue === \"{}\" && rawValue === \"{}\") ||\n                                                        (typeof itemConfig.defaultDisplayValue === 'string' && itemConfig.defaultDisplayValue.includes(\"(暂无\") && rawValue === itemConfig.defaultDisplayValue) ||\n                                                        (typeof itemConfig.defaultDisplayValue === 'string' && itemConfig.defaultDisplayValue.includes(\"(空栏位)\") && rawValue === itemConfig.defaultDisplayValue);\n\n                                if (isDefaultEmptyJson) { shouldRenderItem = false; } \n                                else if (valueIsPlaceholder && String(rawValue) !== itemConfig.defaultDisplayValue) {\n                                    shouldRenderItem = false; \n                                }\n                            } else { \n                                if (valueIsPlaceholder && rawValue !== itemConfig.defaultDisplayValue && itemConfig.id !=='name' && itemConfig.id !== 'currentLocation' ) { \n                                    shouldRenderItem = false; \n                                }\n                            }\n\n\n                            if (shouldRenderItem || (itemConfig.isComplex && itemConfig.id === 'newsItems')) { // For 'internet.newsItems', always attempt to render so empty state is shown\n                                let displayValueHtml = ''; \n                                if (itemConfig.formatUnit && (typeof rawValue === 'string' || typeof rawValue === 'number') && !valueIsPlaceholder && String(rawValue) !== itemConfig.defaultDisplayValue) { displayValueHtml = `<span>${escapeHtml(formatLargeNumber(rawValue))}</span>`; } \n                                else if (itemConfig.isComplex) { \n                                    let complexData;\n                                    let parseErrorForComplex = null;\n                                    try {\n                                        if (typeof rawValue === 'string' && (rawValue.trim().startsWith('{') || rawValue.trim().startsWith('['))) {\n                                            complexData = JSON.parse(rawValue);\n                                        } else if (Array.isArray(rawValue) || (typeof rawValue === 'object' && rawValue !== null)) {\n                                            complexData = rawValue; \n                                        } else {\n                                            complexData = null; // Indicates not a parsable JSON or already parsed non-array/object\n                                            if (rawValue && typeof rawValue === 'string' && rawValue.trim() !== \"\" && rawValue !== itemConfig.defaultDisplayValue) {\n                                                 // It's a string but not JSON, might be an error or just plain text\n                                                 console.warn(`[InfoBarUPM ComplexRender] Value for ${dataKey} is a string but not JSON:`, rawValue);\n                                            }\n                                        }\n                                        // Ensure complexData is what we expect for specific types\n                                        if ((itemConfig.complexType === 'taskList' || itemConfig.complexType.endsWith('List') || itemConfig.complexType === 'newsFeed') && !Array.isArray(complexData) && complexData !== null) {\n                                            console.warn(`[InfoBarUPM ComplexRender] Expected array for ${itemConfig.complexType} at ${dataKey} but got:`, complexData, \"Raw:\", rawValue);\n                                            complexData = null; // Treat as invalid if not an array for list types\n                                            if(String(rawValue).trim() !== '[]') parseErrorForComplex = true; // Mark as parse error if raw was not '[]'\n                                        } else if (itemConfig.complexType === 'task' && (complexData !== null && typeof complexData !== 'object')) {\n                                            console.warn(`[InfoBarUPM ComplexRender] Expected object for ${itemConfig.complexType} at ${dataKey} but got:`, complexData, \"Raw:\", rawValue);\n                                            complexData = null;\n                                            if(String(rawValue).trim() !== '{}') parseErrorForComplex = true;\n                                        }\n\n                                    } catch (e) {\n                                        console.error(`[InfoBarUPM CRITICAL JSON PARSE ERROR] for ${dataKey}:`, e, \"\\nRaw value:\", rawValue);\n                                        complexData = null; \n                                        parseErrorForComplex = true;\n                                    }\n                                    \n                                    if (parseErrorForComplex) {\n                                        displayValueHtml = `<span style=\"color:var(--Danger, red);\">(数据格式错误)</span>`;\n                                    } else if (itemConfig.complexType === 'task' && complexData && typeof complexData === 'object' && complexData.name && !AI_PLACEHOLDERS.some(p => String(complexData.name).includes(p)) && complexData.name !== INFO_CONFIG.panels.tasks.items.find(i => i.id === 'mainQuest').defaultDisplayValue.match(/\"name\":\"([^\"]+)\"/)?.[1] && complexData.name !== INFO_CONFIG.panels.tasks.items.find(i => i.id === 'dailyTask').defaultDisplayValue.match(/\"name\":\"([^\"]+)\"/)?.[1]) {\n                                        // ... (task rendering as before, ensure robustness) ...\n                                        displayValueHtml = `<div class=\"task-data\"><strong>${escapeHtml(String(complexData.name))}</strong> (${escapeHtml(String(complexData.status || '-'))})`;\n                                        if (complexData.description && String(complexData.description).trim() && !AI_PLACEHOLDERS.some(p => String(complexData.description).includes(p))) { displayValueHtml += `<br><small style=\"display:block; margin-top:3px; white-space:pre-wrap;\">${escapeHtml(String(complexData.description))}</small>`; }\n                                        if (complexData.reward) {\n                                            if (typeof complexData.reward === 'object' && complexData.reward !== null && (complexData.reward.type || complexData.reward.details)) {\n                                                const rewardType = complexData.reward.type ? `类型: ${escapeHtml(String(complexData.reward.type))}` : '';\n                                                const rewardDetails = complexData.reward.details ? `详情: ${escapeHtml(String(complexData.reward.details))}` : '';\n                                                if (rewardType || rewardDetails) { displayValueHtml += `<br><small style=\"display:block; margin-top:3px; color:var(--Success, green);\">奖励: ${rewardType} ${rewardDetails}</small>`; }\n                                            } else if (typeof complexData.reward === 'string' && String(complexData.reward).trim() !== \"\" &&  !AI_PLACEHOLDERS.some(p => String(complexData.reward).includes(p))) {\n                                                 displayValueHtml += `<br><small style=\"display:block; margin-top:3px; color:var(--Success, green);\">奖励: ${escapeHtml(String(complexData.reward))}</small>`;\n                                            }\n                                        }\n                                        displayValueHtml += `</div>`;\n                                    } else if (itemConfig.complexType === 'taskList' && Array.isArray(complexData) && complexData.length > 0) {\n                                        let taskListContent = \"\";\n                                        let validTasksInList = 0;\n                                        complexData.forEach((task, index) => {\n                                            try {\n                                                if (task && typeof task === 'object' && task.name && String(task.name).trim() && !AI_PLACEHOLDERS.some(p => String(task.name).includes(p))) {\n                                                    validTasksInList++;\n                                                    let taskItemHtml = `<div class=\"sub-task-item\" style=\"margin-bottom: 5px; padding-bottom: 5px;\"><strong>${escapeHtml(String(task.name))}</strong> (${escapeHtml(String(task.status || '-'))})`;\n                                                    if (task.description && String(task.description).trim() && !AI_PLACEHOLDERS.some(p => String(task.description).includes(p))) { taskItemHtml += `<br><small style=\"display:block; margin-top:3px; white-space:pre-wrap;\">${escapeHtml(String(task.description))}</small>`; }\n                                                    if (task.progress && String(task.progress).trim() && !AI_PLACEHOLDERS.some(p => String(task.progress).includes(p))) { taskItemHtml += `<br><small style=\"display:block; margin-top:3px; color:var(--Primary, dodgerblue);\">进度: ${escapeHtml(String(task.progress))}</small>`;}\n                                                    if (task.reward) {\n                                                        if (typeof task.reward === 'object' && task.reward !== null && (task.reward.type || task.reward.details)) {\n                                                            const rewardType = task.reward.type ? `类型: ${escapeHtml(String(task.reward.type))}` : '';\n                                                            const rewardDetails = task.reward.details ? `详情: ${escapeHtml(String(task.reward.details))}` : '';\n                                                            if (rewardType || rewardDetails) { taskItemHtml += `<br><small style=\"display:block; margin-top:3px; color:var(--Success, green);\">奖励: ${rewardType} ${rewardDetails}</small>`;}\n                                                        } else if (typeof task.reward === 'string' && String(task.reward).trim() !== \"\" && !AI_PLACEHOLDERS.some(p => String(task.reward).includes(p))) {\n                                                            taskItemHtml += `<br><small style=\"display:block; margin-top:3px; color:var(--Success, green);\">奖励: ${escapeHtml(String(task.reward))}</small>`;\n                                                        }\n                                                    }\n                                                    taskItemHtml += `</div>`;\n                                                    taskListContent += taskItemHtml;\n                                                }\n                                            } catch (singleTaskError) { console.error(`[InfoBarUPM TaskList] Error rendering single task: `, task, singleTaskError); taskListContent += `<div>渲染任务'${escapeHtml(task?.name || '未知')}'出错</div>`}\n                                        });\n                                        if (validTasksInList > 0) {\n                                            const $tempDiv = $('<div></div>').html(taskListContent);\n                                            $tempDiv.find('.sub-task-item').each(function(idx, el){ if (idx < validTasksInList - 1) $(el).css('border-bottom', `1px dotted ${uiTheme.itemBorder}`); });\n                                            displayValueHtml = `<div class=\"task-list-container\">${$tempDiv.html()}</div>`;\n                                        } else {\n                                            displayValueHtml = `<span>(无有效支线任务)</span>`;\n                                        }\n                                    } else if (itemConfig.complexType === 'shareholdersList' && Array.isArray(complexData) && complexData.length > 0) {\n                                        let listHtml = '<ul class=\"shareholders-list\" style=\"list-style:none; padding-left:0;\">'; let count = 0;\n                                        complexData.forEach(sh => { if (sh && sh.name && sh.stake && !AI_PLACEHOLDERS.includes(sh.name)) { listHtml += `<li style=\"display:flex; justify-content:space-between;\"><span>${escapeHtml(String(sh.name))}:</span> <span>${escapeHtml(String(sh.stake))}</span></li>`; count++; }});\n                                        listHtml += '</ul>';\n                                        displayValueHtml = count > 0 ? listHtml : `<span>(无有效股权信息)</span>`;\n                                    } else if (itemConfig.complexType === 'projectsListSimple' && Array.isArray(complexData) && complexData.length > 0) {\n                                        let listHtml = '<div class=\"projects-list\">'; let count = 0;\n                                        complexData.forEach((proj, index) => { if (proj && proj.name && !AI_PLACEHOLDERS.includes(proj.name)) { listHtml += `<div class=\"project-item\" style=\"margin-bottom: 5px; padding-bottom: 5px;\"><strong>${escapeHtml(String(proj.name))}</strong> (${escapeHtml(String(proj.status || '-'))})`; if (proj.progress && !AI_PLACEHOLDERS.includes(proj.progress)) { listHtml += `<br><small style=\"display:block; margin-top:3px;\">进度: ${escapeHtml(String(proj.progress))}</small>`; } listHtml += `</div>`; count++; }});\n                                        listHtml += '</div>';\n                                        if (count > 0) {\n                                            const $tempDiv = $('<div></div>').html(listHtml);\n                                            $tempDiv.find('.project-item').each(function(idx, el){ if (idx < count - 1) $(el).css('border-bottom', `1px dotted ${uiTheme.itemBorder}`); });\n                                            displayValueHtml = $tempDiv.html();\n                                        } else { displayValueHtml = `<span>(无有效项目信息)</span>`; }\n                                    } else if (itemConfig.complexType === 'stringListSimple' && Array.isArray(complexData) && complexData.length > 0) {\n                                        let listHtml = '<ul style=\"list-style-type: disc; padding-left: 20px; margin:0;\">'; let count = 0;\n                                        complexData.forEach(str => { if (typeof str === 'string' && str.trim() !== '' && !AI_PLACEHOLDERS.includes(str)) { listHtml += `<li>${escapeHtml(str)}</li>`; count++; } });\n                                        listHtml += '</ul>';\n                                        displayValueHtml = count > 0 ? listHtml : `<span>(列表为空或无有效条目)</span>`;\n                                    } else if (itemConfig.complexType === 'newsFeed' && panelId === 'internet' && itemConfig.id === 'newsItems' ) {\n                                        // console.log(`[InfoBar Web Render] newsItems. Raw:`, rawValue, `Parsed complexData:`, complexData);\n                                        if (Array.isArray(complexData) && complexData.length > 0) {\n                                            displayValueHtml = `<div class=\"news-feed-container\">`;\n                                            let newsRenderedCount = 0;\n                                            complexData.slice(0, 3).forEach(news => { \n                                                if (news && typeof news === 'object' && news.content && String(news.content).trim() && !AI_PLACEHOLDERS.some(p=> String(news.content).includes(p))) {\n                                                    newsRenderedCount++;\n                                                    displayValueHtml += `<div class=\"news-item\"> <!-- News item start -->\n                                                        <div class=\"news-header\">\n                                                            <span class=\"news-platform\">P: ${escapeHtml(String(news.platform || 'N/A'))}</span>\n                                                            <span class=\"news-author\">A: ${escapeHtml(String(news.author || 'N/A'))}</span>\n                                                            <span class=\"news-timestamp\">T: ${escapeHtml(String(news.timestamp || ''))}</span>\n                                                        </div>\n                                                        <div class=\"news-content\">${escapeHtml(String(news.content))}</div>\n                                                        ${/* ... (images, video, interactions, comments) ... */ ''}\n                                                    </div> <!-- News item end -->`;\n                                                }\n                                            });\n                                            if (newsRenderedCount === 0) displayValueHtml += `<span style=\"color: var(--infobar-text-muted); font-style: italic;\">(无有效新闻)</span>`; \n                                            displayValueHtml += `</div>`;\n                                        } else {\n                                            displayValueHtml = `<span>(暂无新闻)</span>`;\n                                        }\n                                    }\n                                    else { // Fallback for other complex types or if complexData parsing failed/resulted in non-array/object\n                                        if (complexData === null && rawValue && rawValue !== itemConfig.defaultDisplayValue && !valueIsPlaceholder) {\n                                             displayValueHtml = `<span>${escapeHtml(String(rawValue).replace(/\\n/g, '<br>'))} (格式或内容出错)</span>`;\n                                        } else if (typeof rawValue === 'object' && rawValue !== null) { \n                                            displayValueHtml = `<span>${escapeHtml(JSON.stringify(rawValue).replace(/\\n/g, '<br>'))}</span>`; \n                                        } else { \n                                             displayValueHtml = `<span>${escapeHtml(String(rawValue).replace(/\\n/g, '<br>'))}</span>`;\n                                        }\n                                    }\n                                } else { \n                                    displayValueHtml = `<span>${String(rawValue).replace(/\\n/g, '<br>')}</span>`; \n                                }\n                                panelItemsHtml += `<div class=\"infobar-display-item ${itemConfig.isComplex ? 'complex-item' : ''}\"><span class=\"infobar-display-label\">${escapeHtml(itemConfig.label)}:</span><span class=\"infobar-display-value\">${displayValueHtml}</span></div>`; \n                                itemsRenderedForThisPanel++;\n                            }\n                        } \n                    }); \n                    if (itemsRenderedForThisPanel > 0) { hasPanelItemsCurrentSection = true; }\n                } \n                \n                if (hasPanelItemsCurrentSection) { \n                    hasAnyContent = true; \n                    const iconClass = panelConfig.icon || 'fa-question-circle'; \n                    const collapseIcon = infoBarDefaultCollapsed ? 'fa-chevron-right' : 'fa-chevron-down'; \n                    infoBarHtml += `<div class=\"infobar-display-section\"><div class=\"infobar-display-title\" data-panel-id=\"${panelId}\"><i class=\"fa-solid ${collapseIcon} infobar-collapse-icon\"></i><i class=\"fa-solid ${iconClass} infobar-panel-icon\"></i>${escapeHtml(panelConfig.label)}</div><div class=\"infobar-display-content ${infoBarDefaultCollapsed ? 'collapsed' : ''}\">${panelItemsHtml}</div></div>`; \n                }\n            } \n            infoBarHtml += `</div>`; \n            return (hasAnyContent || topTimeHtml) ? (topTimeHtml + infoBarHtml) : ''; \n        }, null, 'renderInfoBarHTML')(); \n    }\n    async function handleMessageRendering(message_id) { \n        await errorCatched(async () => { \n            const $messageNode = retrieveDisplayedMessage(message_id); \n            if (!$messageNode || $messageNode.length === 0 || $messageNode.attr('is_user') === 'true') return; \n            \n            const currentScrollTop = $messageNode.closest('#chat').scrollTop();\n            const isScrolledToBottom = Math.abs($messageNode.closest('#chat')[0].scrollHeight - $messageNode.closest('#chat').scrollTop() - $messageNode.closest('#chat').outerHeight()) < 5;\n\n            $messageNode.find(`.${RENDERED_INFO_BAR_CLASS}`).remove(); \n            $messageNode.find(`.${TOP_TIME_DISPLAY_CLASS}`).remove(); \n            $messageNode.find(`style[data-infobar-style]`).remove(); \n            \n            setTimeout(async () => { \n                let originalMessageText = ''; \n                let aiSummaryText = null;\n                let summaryTypeFromAI = null;\n\n                try { \n                    const messages = await getChatMessages(message_id); \n                    if (messages && messages.length > 0) {\n                        originalMessageText = messages[0].message;\n                        const summaryMatch = originalMessageText.match(SUMMARY_TEXT_REGEX);\n                        if (summaryMatch && summaryMatch[1]) {\n                            aiSummaryText = summaryMatch[1].trim();\n                            const typeMatch = summaryMatch[0].match(/type=\"([^\"]+)\"/);\n                            if (typeMatch && typeMatch[1]) summaryTypeFromAI = typeMatch[1];\n                        }\n                    }\n                } \n                catch(e) { \n                    const $mNode = $messageNode.find('.mes_text').first(); \n                    if ($mNode.length) originalMessageText = $mNode.text(); \n                    console.warn(\"[InfoBarUPM] Failed to get message via API, using .mes_text:\", e);\n                } \n                if (!originalMessageText && !aiSummaryText) {\n                    console.warn(\"[InfoBarUPM] No original message text and no AI summary text for message ID \" + message_id + \". Aborting render.\");\n                    return; \n                }\n                \n                const extractedData = parseAIDataBlock(originalMessageText); \n                let chatVarData = await loadInfoBarDataFromChatVars() || {};\n                chatVarData.summary = chatVarData.summary || { aiTurnCount: 0, lastBigSummaryMsgCount: 0, lastSmallSummaryMsgCount: 0, bigSummaryInterval: 30, smallSummaryInterval: 10, enableBigSummary: true, enableSmallSummary: true, lastBigSummaryTitle: \"\", lastSmallSummaryTitle: \"\" };\n                if (!chatVarData.summary.hasOwnProperty('aiTurnCount')) chatVarData.summary.aiTurnCount = 0;\n\n\n                if (!extractedData && !aiSummaryText) { \n                    aiFailedToOutputThinkbiaoCount++;\n                    let errorMsg =`[InfoBarUPM] AI未输出或输出了无效的<thinkbiao>数据块 (消息ID: ${message_id})。`;\n                    if (aiFailedToOutputThinkbiaoCount >= 2) { /* ... error notification ... */ }\n                    console.warn(errorMsg);\n                    \n                    const defaultSummaryVals = INFO_CONFIG.panels.summary.items.reduce((acc,item) => { /* ... */ return acc; }, { aiTurnCount: chatVarData.summary.aiTurnCount }); \n                    const hasMeaningfulOldData = Object.keys(chatVarData).some(key => { /* ... */ return false; });\n\n                    if (hasMeaningfulOldData) {\n                         const oldInfoBarHtml = renderInfoBarHTML(chatVarData);\n                         if (oldInfoBarHtml) {\n                            /* ... prepend style and append HTML, re-bind events ... */\n                         }\n                    } else {\n                        $messageNode.append(`<div class=\"${RENDERED_INFO_BAR_CLASS} theme-${currentTheme === 'auto' ? ($('body', window.parent.document).hasClass('dark') ? 'dark' : 'light') : currentTheme}\" style=\"padding:10px; border:1px solid var(--Danger, red); color:var(--Danger, red); border-radius:4px;\">信息栏数据解析失败，且无先前数据可显示。</div>`);\n                    }\n                    return; \n                }\n                if (extractedData) aiFailedToOutputThinkbiaoCount = 0; \n                \n                if (extractedData) { \n                    chatVarData.summary.aiTurnCount = (chatVarData.summary.aiTurnCount || 0) + 1;\n                }\n\n                let mergedData = { ...chatVarData }; \n                mergedData.trackedNPCs = mergedData.trackedNPCs || {}; \n                \n                // NPC ID Deduplication & Merging (Simplified attempt)\n                let nameToMainIdMap = {};\n                if (mergedData.trackedNPCs && Object.keys(mergedData.trackedNPCs).length > 0) {\n                    for (const npcId in mergedData.trackedNPCs) {\n                        const npc = mergedData.trackedNPCs[npcId];\n                        if (npc && npc.name && !AI_PLACEHOLDERS.includes(npc.name)) {\n                            if (!nameToMainIdMap[npc.name]) {\n                                nameToMainIdMap[npc.name] = npcId;\n                            } else {\n                                // Duplicate name found with a different ID, attempt to merge to the first encountered ID\n                                const mainId = nameToMainIdMap[npc.name];\n                                if (mainId !== npcId) { \n                                    // console.log(`[InfoBarUPM Dedupe] Merging ${npc.name} from ${npcId} to ${mainId}`);\n                                    for (const attr in npc) {\n                                        // Prioritize more complete data or non-placeholder data\n                                        if (npc[attr] !== undefined && (mergedData.trackedNPCs[mainId][attr] === undefined || AI_PLACEHOLDERS.includes(String(mergedData.trackedNPCs[mainId][attr])) && !AI_PLACEHOLDERS.includes(String(npc[attr])))) {\n                                            mergedData.trackedNPCs[mainId][attr] = npc[attr];\n                                        }\n                                    }\n                                    delete mergedData.trackedNPCs[npcId]; // Remove the duplicate entry\n                                }\n                            }\n                        }\n                    }\n                }\n                // Clean sole \"(数据获取失败)\" entry if it's the only one\n                if (mergedData.trackedNPCs && Object.keys(mergedData.trackedNPCs).length === 1 && mergedData.trackedNPCs[\"(数据获取失败)\"]) {\n                    mergedData.trackedNPCs = {};\n                }\n\n\n                if (extractedData) { \n                    for (let i = 1; i <= MAX_AI_NPCS; i++) {\n                        const idKey = `npc${i}.id`; \n                        let npcId = extractedData[idKey];\n                        \n                        if (npcId && String(npcId).trim() !== \"\" && !AI_PLACEHOLDERS.includes(npcId) ) {\n                            const npcNameFromAI = extractedData[`npc${i}.name`];\n                             // Use existing main ID if name matches, otherwise use AI's ID\n                            if (npcNameFromAI && nameToMainIdMap[npcNameFromAI] && nameToMainIdMap[npcNameFromAI] !== npcId) {\n                                // console.log(`[InfoBarUPM Dedupe from AI] AI gave ID ${npcId} for ${npcNameFromAI}, but main ID is ${nameToMainIdMap[npcNameFromAI]}. Using main ID.`);\n                                npcId = nameToMainIdMap[npcNameFromAI];\n                            } else if (npcNameFromAI && !nameToMainIdMap[npcNameFromAI]) {\n                                nameToMainIdMap[npcNameFromAI] = npcId; // Store new name-ID mapping\n                            }\n\n\n                            if (!mergedData.trackedNPCs[npcId]) {\n                                mergedData.trackedNPCs[npcId] = { id: npcId }; \n                            }\n                            // Always update name if provided by AI, or use ID as name if name is missing/placeholder\n                            if (npcNameFromAI && !AI_PLACEHOLDERS.includes(npcNameFromAI)) { \n                                mergedData.trackedNPCs[npcId].name = npcNameFromAI;\n                            } else if (!mergedData.trackedNPCs[npcId].name || AI_PLACEHOLDERS.includes(mergedData.trackedNPCs[npcId].name)) { \n                                 mergedData.trackedNPCs[npcId].name = npcId; \n                            }\n\n                            INFO_CONFIG.panels.interactionDisplay.npcObjectStructure.forEach(attrConf => {\n                                const npcAttrKey = `npc${i}.${attrConf.id}`; \n                                if (extractedData[npcAttrKey] !== undefined) {\n                                    const aiValue = extractedData[npcAttrKey];\n                                    const isAINpcPlaceholder = AI_PLACEHOLDERS.includes(String(aiValue)) && String(aiValue) !== attrConf.defaultDisplayValue;\n                                    const existingNpcAttrValue = mergedData.trackedNPCs[npcId][attrConf.id];\n                                    \n                                    if (!isAINpcPlaceholder || aiValue === attrConf.defaultDisplayValue) { // AI provided specific value or the exact default\n                                        mergedData.trackedNPCs[npcId][attrConf.id] = aiValue;\n                                    } else if (existingNpcAttrValue === undefined || AI_PLACEHOLDERS.includes(String(existingNpcAttrValue))) { \n                                        // AI provided placeholder, and old value was undefined or also a placeholder -> use default\n                                        mergedData.trackedNPCs[npcId][attrConf.id] = attrConf.defaultDisplayValue; \n                                    }\n                                    // If AI provided placeholder, and old value was specific, old specific value is kept (implicitly by not overwriting)\n                                } else if (mergedData.trackedNPCs[npcId][attrConf.id] === undefined) { // AI did not provide this attribute\n                                     mergedData.trackedNPCs[npcId][attrConf.id] = attrConf.defaultDisplayValue;\n                                }\n                            });\n                            if (i === 1 && npcId !== \"(数据获取失败)\") { // Ensure lastInteractedNPCId is valid\n                                mergedData.lastInteractedNPCId = npcId;\n                            }\n                        }\n                    }\n\n                    if (extractedData['interactionTargets.list']) {\n                        mergedData['interactionTargets.list'] = extractedData['interactionTargets.list'];\n                        try {\n                            const selectorNpcs = JSON.parse(extractedData['interactionTargets.list']);\n                            if(Array.isArray(selectorNpcs)){\n                                selectorNpcs.forEach(npc => {\n                                    if (npc && npc.id && npc.name && typeof npc.id === 'string' && typeof npc.name === 'string' && npc.id !== \"(数据获取失败)\") { \n                                        // Deduplication check based on name for selector list too\n                                        let finalIdForSelectorNpc = npc.id;\n                                        if (nameToMainIdMap[npc.name] && nameToMainIdMap[npc.name] !== npc.id) {\n                                            finalIdForSelectorNpc = nameToMainIdMap[npc.name];\n                                        } else if (!nameToMainIdMap[npc.name]) {\n                                            nameToMainIdMap[npc.name] = npc.id;\n                                        }\n\n                                        if (!mergedData.trackedNPCs[finalIdForSelectorNpc]) { \n                                            mergedData.trackedNPCs[finalIdForSelectorNpc] = { id: finalIdForSelectorNpc, name: npc.name };\n                                            INFO_CONFIG.panels.interactionDisplay.npcObjectStructure.forEach(attrConf => {\n                                                if (mergedData.trackedNPCs[finalIdForSelectorNpc][attrConf.id] === undefined) {\n                                                    mergedData.trackedNPCs[finalIdForSelectorNpc][attrConf.id] = attrConf.defaultDisplayValue;\n                                                }\n                                            });\n                                        } else { \n                                            mergedData.trackedNPCs[finalIdForSelectorNpc].name = npc.name; \n                                        }\n                                    }\n                                });\n                            }\n                        } catch(e) { console.warn(\"[InfoBarUPM] Error parsing interactionTargets.list for trackedNPCs update:\", e); }\n                    } else if (!mergedData['interactionTargets.list']) {\n                        mergedData['interactionTargets.list'] = '[]'; // Default to empty array string if not provided\n                    }\n\n                    for (const key in extractedData) { \n                        const isNpcCoreField = key.startsWith('npc') && (key.includes('.id') || INFO_CONFIG.panels.interactionDisplay.npcObjectStructure.some(s => key.includes(`.${s.id}`)));\n                        if (isNpcCoreField || key === 'interactionTargets.list') continue; \n\n                        const aiValue = extractedData[key];\n                        const existingValue = mergedData[key];\n                        const isAiPlaceholder = AI_PLACEHOLDERS.includes(String(aiValue));\n                        const isExistingValueValid = existingValue !== undefined && !AI_PLACEHOLDERS.includes(String(existingValue));\n\n                        const parts = key.split('.');\n                        let panelId_from_key = parts[0];\n                        let itemId_from_key = parts.slice(1).join('.');\n                        const panelConfig = INFO_CONFIG.panels[panelId_from_key];\n                        let itemConfig = null;\n                        if (panelConfig && panelConfig.items && Array.isArray(panelConfig.items)) { // Ensure panelConfig.items is an array\n                            itemConfig = panelConfig.items.find(it => it.id === itemId_from_key);\n                        }\n                        \n                        if (key === 'personal.currentLocation') {\n                            const aiValueIsLocationPlaceholder = AI_PLACEHOLDERS.some(p => String(aiValue).toLowerCase().includes(\"ai应根据对话\") || String(aiValue).toLowerCase().includes(\"(ai根据\"));\n                            const existingValueIsSpecificLocation = existingValue && !AI_PLACEHOLDERS.some(p => String(existingValue).toLowerCase().includes(\"ai应根据对话\") || String(existingValue).toLowerCase().includes(\"(ai根据\"));\n                            \n                            if (aiValueIsLocationPlaceholder && existingValueIsSpecificLocation) {\n                                mergedData[key] = existingValue;\n                            } else {\n                                mergedData[key] = aiValue;\n                            }\n                            continue;\n                        }\n                        \n\n                        if (!isAiPlaceholder) { \n                            mergedData[key] = aiValue;\n                        } else if (isAiPlaceholder && !isExistingValueValid) { \n                            if (itemConfig && itemConfig.defaultDisplayValue !== undefined) { \n                                mergedData[key] = itemConfig.defaultDisplayValue; \n                            } else {\n                                mergedData[key] = aiValue; \n                            }\n                        }\n                    } \n                    if (extractedData['worldTime.date'] !== undefined) mergedData['worldTime.date'] = extractedData['worldTime.date']; \n                    if (extractedData['worldTime.time'] !== undefined) mergedData['worldTime.time'] = extractedData['worldTime.time']; \n                    if (extractedData['worldTime.weather'] !== undefined) mergedData['worldTime.weather'] = extractedData['worldTime.weather'];\n                    \n                    for (const panelId_loop in INFO_CONFIG.panels) { // Renamed panelId to panelId_loop to avoid conflict\n                        const panelConfig_loop = INFO_CONFIG.panels[panelId_loop]; // Renamed\n                        if (panelConfig_loop.isUtilityPanel || panelId_loop === 'worldTime' || !currentSettings[panelId_loop]?.enabled || panelId_loop === 'interactionDisplay' || panelId_loop === 'summary') continue; \n                        if (panelConfig_loop.isListPanel) { \n                            const listKey = `${panelId_loop}.list`; \n                            if (mergedData[listKey] === undefined && !(panelConfig_loop.itemKeyPrefix && Object.keys(mergedData).some(k => k.startsWith(panelConfig_loop.itemKeyPrefix)))) {\n                                mergedData[listKey] = panelConfig_loop.defaultDisplayValue;\n                            }\n                        } else if (panelConfig_loop.items && Array.isArray(panelConfig_loop.items)) { \n                            panelConfig_loop.items.forEach(itemConfig_loop => { // Renamed\n                                if (currentSettings[panelId_loop].itemsEnabled[itemConfig_loop.id]) { \n                                    const dataKey = `${panelId_loop}.${itemConfig_loop.id}`; \n                                    const isPlaceholderOrUndefinedInMerged = mergedData[dataKey] === undefined || AI_PLACEHOLDERS.some(p => String(mergedData[dataKey]).toLowerCase().includes(p.toLowerCase()));\n                                    if (isPlaceholderOrUndefinedInMerged && itemConfig_loop.defaultDisplayValue !== undefined) { \n                                         mergedData[dataKey] = itemConfig_loop.defaultDisplayValue;\n                                    }\n                                } \n                            }); \n                        } \n                    } \n                } \n\n                if (mergedData['personal.name'] && !AI_PLACEHOLDERS.includes(String(mergedData['personal.name']))) {\n                    lastKnownCharNameFromContext = String(mergedData['personal.name']);\n                    const localParentDoc = window.parent.document;\n                    if ($(`#${POPUP_ID}`, localParentDoc).is(':visible') && $('#infobar-tab-backupManagement', localParentDoc).is('.active')) {\n                        updateBackupPanelList(); \n                    }\n                    updateBackupStatusWidget(); \n                }\n\n                if (!mergedData.userSelectedNPCIdForDisplay && mergedData.lastInteractedNPCId && mergedData.lastInteractedNPCId !== \"(数据获取失败)\" ) {\n                    mergedData.userSelectedNPCIdForDisplay = mergedData.lastInteractedNPCId;\n                }\n                if ((!mergedData.userSelectedNPCIdForDisplay || mergedData.userSelectedNPCIdForDisplay === \"(数据获取失败)\") && Object.keys(mergedData.trackedNPCs).length > 0) {\n                    const firstValidNpcId = Object.keys(mergedData.trackedNPCs).find(id => id !== \"(数据获取失败)\");\n                    if (firstValidNpcId) mergedData.userSelectedNPCIdForDisplay = firstValidNpcId;\n                }\n                \n                let chatVarsToUpdate_summary = await getVariables({type: 'chat'}) || {};\n                const summaryTriggerType = chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.lastTriggeredType'];\n                const summaryTriggerMsgCount = chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.summaryTriggerMsgCount'];\n\n                if (aiSummaryText && summaryTriggerType && summaryTriggerMsgCount !== undefined) {\n                    // console.log(`[InfoBarUPM] Processing received ${summaryTypeFromAI || summaryTriggerType} summary.`);\n                    try {\n                        const finalSummaryType = summaryTypeFromAI || summaryTriggerType; \n                        const summaryTitle = `剧情总结_${finalSummaryType}_${new Date().toISOString().slice(0,10).replace(/-/g,'')}_Turn${mergedData.summary.aiTurnCount}`; // Use current AI turn for title\n                        const summaryKeywords = [\"剧情总结\", \"infobar_summary\", finalSummaryType, `Turn${mergedData.summary.aiTurnCount}`];\n                        \n                        if (typeof saveWorldInfoEntry === 'function') {\n                             await saveWorldInfoEntry({\n                                keys: summaryKeywords,\n                                content: aiSummaryText,\n                                title: summaryTitle, \n                                id: summaryTitle, \n                                selective: true, \n                                constant: false, \n                                hidden: false,\n                             });\n                             notifyUser(`${finalSummaryType} 总结已保存至世界书: ${summaryTitle}`, 'success');\n                             if (finalSummaryType === 'big') {\n                                mergedData.summary.lastBigSummaryMsgCount = mergedData.summary.aiTurnCount; \n                                mergedData.summary.lastBigSummaryTitle = summaryTitle;\n                             } else { \n                                mergedData.summary.lastSmallSummaryMsgCount = mergedData.summary.aiTurnCount; \n                                mergedData.summary.lastSmallSummaryTitle = summaryTitle;\n                             }\n                        } else {\n                            console.warn(\"[InfoBarUPM] saveWorldInfoEntry function not available. Cannot save summary.\");\n                            notifyUser(\"无法保存总结到世界书 (功能未找到). 总结见控制台。\", \"warning\", 7000);\n                             console.log(`[InfoBarUPM Summary Fallback] Title: ${summaryTitle}\\nContent:\\n${aiSummaryText}`);\n                        }\n                    } catch (e) {\n                        console.error(\"[InfoBarUPM] Error saving summary to world book:\", e);\n                        notifyUser(\"保存总结到世界书失败\", \"error\");\n                    }\n                    delete chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.needsBigSummary'];\n                    delete chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.needsSmallSummary'];\n                    delete chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.lastTriggeredType'];\n                    delete chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.summaryTriggerMsgCount'];\n                    await replaceVariables(chatVarsToUpdate_summary, {type:'chat'});\n                }\n                \n                if (!aiSummaryText && extractedData) { \n                    const currentAiTurn = mergedData.summary.aiTurnCount || 0;\n                    let needsSummaryFlag = false;\n                    const settings = currentSettings.summary || {}; \n                    const sumData = mergedData.summary || {}; \n\n                    const bigInterval = parseInt(sumData.bigSummaryInterval, 10) || parseInt(INFO_CONFIG.panels.summary.items.find(i=>i.id==='bigSummaryInterval').defaultDisplayValue, 10);\n                    const smallInterval = parseInt(sumData.smallSummaryInterval, 10) || parseInt(INFO_CONFIG.panels.summary.items.find(i=>i.id==='smallSummaryInterval').defaultDisplayValue, 10);\n                    const enableBig = settings.itemsEnabled?.enableBigSummary !== undefined ? settings.itemsEnabled.enableBigSummary : INFO_CONFIG.panels.summary.items.find(i=>i.id==='enableBigSummary').defaultEnabled;\n                    const enableSmall = settings.itemsEnabled?.enableSmallSummary !== undefined ? settings.itemsEnabled.enableSmallSummary : INFO_CONFIG.panels.summary.items.find(i=>i.id==='enableSmallSummary').defaultEnabled;\n\n\n                    if (enableBig && bigInterval > 0 && (currentAiTurn - (sumData.lastBigSummaryMsgCount || 0)) >= bigInterval) {\n                        if (!chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.needsBigSummary']) { \n                            chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.needsBigSummary'] = true;\n                            needsSummaryFlag = true;\n                        }\n                    } else if (enableSmall && smallInterval > 0 && (currentAiTurn - (sumData.lastSmallSummaryMsgCount || 0)) >= smallInterval) {\n                        if (!chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.needsSmallSummary']) { \n                            chatVarsToUpdate_summary['infobar_upm_character_data_v1.summary.needsSmallSummary'] = true;\n                            needsSummaryFlag = true;\n                        }\n                    }\n                    if (needsSummaryFlag) {\n                         await replaceVariables(chatVarsToUpdate_summary, {type:'chat'});\n                    }\n                }\n\n                await saveInfoBarDataToChatVars(mergedData); \n                const infoBarHtml = renderInfoBarHTML(mergedData); \n                \n                if (infoBarHtml) { \n                    const $infoBarStyle = $(`<style data-infobar-style>${getRenderedInfoBarStyle()}</style>`); \n                    $messageNode.prepend($infoBarStyle); \n                    const $infoBarElement = $(infoBarHtml); \n                    $messageNode.append($infoBarElement); \n                    \n                    $infoBarElement.find(`#${NPC_SELECTOR_ID}`).on('change', async function() { \n                        const selectedId = $(this).val();\n                        let currentChatData = await loadInfoBarDataFromChatVars() || {};\n                        currentChatData.userSelectedNPCIdForDisplay = selectedId;\n                        await saveInfoBarDataToChatVars(currentChatData); \n                        const trackedNpcs = currentChatData.trackedNPCs || {};\n                        if (trackedNpcs[selectedId] && trackedNpcs[selectedId].id !== \"(数据获取失败)\") { // Added check\n                             $(`#${NPC_DETAIL_CONTAINER_ID}`, $messageNode).html(renderSingleNPCDetailsHTML(trackedNpcs[selectedId], INFO_CONFIG.panels.interactionDisplay, currentSettings.interactionDisplay));\n                        } else {\n                             $(`#${NPC_DETAIL_CONTAINER_ID}`, $messageNode).html(`<div style=\"text-align:center; padding: 10px 0; color: var(--infobar-text-muted);\">(无效的NPC选择)</div>`);\n                        }\n                    });\n                    $infoBarElement.find('.infobar-display-title').on('click', function() { \n                        $(this).next('.infobar-display-content').toggleClass('collapsed'); \n                        $(this).find('.infobar-collapse-icon').toggleClass('fa-chevron-right fa-chevron-down');\n                    }); \n                } \n                if (!isScrolledToBottom) { $messageNode.closest('#chat').scrollTop(currentScrollTop); } \n                else { $messageNode.closest('#chat').scrollTop($messageNode.closest('#chat')[0].scrollHeight); }\n            }, 350); \n        }, null, 'handleMessageRenderingOuter')(); \n    }\n    function getRenderedInfoBarStyle() { return errorCatched(() => { let isDark; let finalTheme = currentTheme; if (currentTheme === 'auto') { isDark = $('body', window.parent.document).hasClass('dark') || $('body', window.parent.document).hasClass('dark_mode'); finalTheme = isDark ? 'dark' : 'light'; } else { isDark = ['dark', 'cyberpunk', 'steampunk'].includes(currentTheme); } const scrollThumbColor = isDark ? '#555' : '#bbb'; const scrollTrackColor = isDark ? '#2b2d31' : '#f0f0f0'; let theme = { bg: isDark ? '#2a2a2a' : '#FFFFFF', text: isDark ? '#d8d8d8' : '#333333', sectionBg: isDark ? '#333333' : '#fdfdfd', sectionBorder: isDark ? '#444444' : '#f0f0f0', titleColor: isDark ? '#a6c5e9' : '#4a5568', titleBorder: isDark ? 'rgba(100, 105, 115, 0.5)' : '#e0e0e0', itemLabelColor: isDark ? '#9cb1c9' : '#5a67d8', itemValueColor: isDark ? '#dadada' : '#2d3748', itemBorder: isDark ? 'rgba(255,255,255,0.08)' : '#e2e8f0', hoverBg: isDark ? 'rgba(255,255,255,0.05)' : '#f7f9fc', iconColor: isDark ? '#8a99af' : '#718096', fontFamily: \"'Segoe UI', Roboto, sans-serif\" }; if (finalTheme === 'cyberpunk') { theme = {...theme, bg: '#0d0221', text: '#95f0f5', sectionBg: '#1a0a3d', sectionBorder: '#4f208c', titleColor: '#f0f57a', titleBorder: '#7c3eff', itemLabelColor: '#ff55a3', itemValueColor: '#b3faff', itemBorder: 'rgba(124,62,255,0.3)', hoverBg: '#2c1a52', iconColor: '#95f0f5', fontFamily: \"'Orbitron', monospace, sans-serif\"}; } else if (finalTheme === 'steampunk') { theme = {...theme, bg: '#e6d8b8', text: '#5a3e2b', sectionBg: '#d8c0a0', sectionBorder: '#a07e5f', titleColor: '#7a5032', titleBorder: '#8c6d52', itemLabelColor: '#8B4513', itemValueColor: '#4a2c1a', itemBorder: 'rgba(139,69,19,0.2)', hoverBg: '#c9ae8d', iconColor: '#6b4f3a', fontFamily: \"'IM Fell DW Pica', serif\"}; } else if (finalTheme === 'minimalist-modern') { theme = {...theme, bg: isDark ? '#1f1f1f' : '#ffffff', text: isDark ? '#e0e0e0' : '#212121', sectionBg: isDark ? '#2f2f2f' : '#f9f9f9', sectionBorder: isDark ? '#424242' : '#eeeeee', titleColor: isDark ? '#bbbbbb' : '#424242', titleBorder: isDark ? '#505050' : '#e0e0e0', itemLabelColor: isDark ? '#9e9e9e' : '#616161', itemValueColor: isDark ? '#f0f0f0' : '#333333', hoverBg: isDark ? '#3a3a3a' : '#f0f0f0', iconColor: isDark ? '#bdbdbd' : '#757575'}; } else if (finalTheme === 'parchment-magical') { theme = {...theme, bg: '#fdf5e6', text: '#5d4037', sectionBg: '#f7efdd', sectionBorder: '#d2b48c', titleColor: '#8d6e63', titleBorder: '#bcaaa4', itemLabelColor: '#795548', itemValueColor: '#4e342e', itemBorder: 'rgba(121,85,72,0.2)', hoverBg: '#f0e6d0', iconColor: '#a1887f', fontFamily: \"'EB Garamond', serif\"}; } const topTimeBg = (finalTheme === 'cyberpunk' || finalTheme === 'steampunk' || finalTheme === 'parchment-magical') ? theme.sectionBg : (isDark ? 'var(--bg2, #303030)' : '#f0f0f0'); \n            const newsFeedCss = `\n            .${RENDERED_INFO_BAR_CLASS} .news-feed-container { max-height: 250px; overflow-y: auto; scrollbar-width: thin; padding-right: 5px; border: 1px solid ${theme.itemBorder}; border-radius: 4px; padding: 5px; }\n            .${RENDERED_INFO_BAR_CLASS} .news-item { margin-bottom: 10px; padding: 8px; border: 1px solid ${theme.itemBorder}; border-radius: 4px; background-color: ${isDark ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)'}; }\n            .${RENDERED_INFO_BAR_CLASS} .news-item:last-child { margin-bottom: 0; }\n            .${RENDERED_INFO_BAR_CLASS} .news-header { display: flex; justify-content: space-between; align-items: center; font-size: 0.85em; color: ${theme.itemLabelColor}; margin-bottom: 4px; flex-wrap: wrap; }\n            .${RENDERED_INFO_BAR_CLASS} .news-platform { font-weight: bold; margin-right: 8px; }\n            .${RENDERED_INFO_BAR_CLASS} .news-author { font-style: italic; margin-right: 8px; }\n            .${RENDERED_INFO_BAR_CLASS} .news-timestamp { font-size: 0.9em; opacity: 0.8; }\n            .${RENDERED_INFO_BAR_CLASS} .news-content { margin-bottom: 5px; line-height: 1.4; white-space: pre-wrap; word-break: break-word; }\n            .${RENDERED_INFO_BAR_CLASS} .news-images, .${RENDERED_INFO_BAR_CLASS} .news-video { font-size: 0.8em; color: var(--infobar-text-muted, #888); margin-bottom: 3px; }\n            .${RENDERED_INFO_BAR_CLASS} .news-interactions { display: flex; gap: 10px; font-size: 0.85em; color: ${theme.itemLabelColor}; margin-bottom: 5px; flex-wrap: wrap; }\n            .${RENDERED_INFO_BAR_CLASS} .news-comments-section { margin-top: 8px; font-size: 0.9em; border-top: 1px dashed ${theme.itemBorder}; padding-top: 6px; }\n            .${RENDERED_INFO_BAR_CLASS} .news-comments-section strong { font-weight:500; display: block; margin-bottom: 3px; }\n            .${RENDERED_INFO_BAR_CLASS} .news-comments-section ul { list-style: none; padding-left: 0; margin: 3px 0 0 0; }\n            .${RENDERED_INFO_BAR_CLASS} .news-comments-section li { padding: 3px 0; border-bottom: 1px dotted ${isDark ? 'rgba(255,255,255,0.06)' : 'rgba(0,0,0,0.06)'}; }\n            .${RENDERED_INFO_BAR_CLASS} .news-comments-section li:last-child { border-bottom: none; }\n            .${RENDERED_INFO_BAR_CLASS} .news-comments-section li strong { display:inline; margin-right: 4px;}\n            `;\n            return `.${RENDERED_INFO_BAR_CLASS}.theme-${finalTheme} { font-family: ${theme.fontFamily}; } .${TOP_TIME_DISPLAY_CLASS} { display: flex; justify-content: space-around; align-items: center; padding: 6px 10px; margin-bottom: 8px; background-color: ${topTimeBg}; color: ${theme.text}; border-radius: 6px; font-size: 0.9em; box-shadow: 0 1px 2px rgba(0,0,0,0.05); border: 1px solid ${theme.sectionBorder}; } .${TOP_TIME_DISPLAY_CLASS} span { display: inline-flex; align-items: center; gap: 5px; opacity: 0.9; } .${TOP_TIME_DISPLAY_CLASS} i { opacity: 0.7; } .${RENDERED_INFO_BAR_CLASS} { font-size: 13.5px; color: ${theme.text}; max-width: 100%; width: 100%; margin: 0 auto; padding: 0; background-color: transparent; border-radius: 0; box-shadow: none; box-sizing: border-box; } .${RENDERED_INFO_BAR_CLASS} .infobar-display-section { margin-bottom: 10px; background-color: ${theme.sectionBg}; border: 1px solid ${theme.sectionBorder}; border-radius: 8px; overflow: hidden; transition: box-shadow 0.2s ease;} .${RENDERED_INFO_BAR_CLASS} .infobar-display-title { font-size: 1.05em; font-weight: 500; padding: 9px 12px; border-bottom: 1px solid ${theme.titleBorder}; color: ${theme.titleColor}; display: flex; align-items: center; cursor: pointer; user-select: none; transition: background-color 0.2s ease;} .${RENDERED_INFO_BAR_CLASS} .infobar-display-title:hover { background-color: ${theme.hoverBg}; } .${RENDERED_INFO_BAR_CLASS} .infobar-collapse-icon { margin-right: 8px; width: 12px; text-align:center; transition: transform 0.25s ease-out; opacity: 0.8; color: ${theme.iconColor};} .${RENDERED_INFO_BAR_CLASS} .infobar-panel-icon { margin-right: 7px; opacity: 0.8; font-size: 0.95em; color: ${theme.iconColor};} .${RENDERED_INFO_BAR_CLASS} .infobar-display-content { padding: 5px 12px 10px 12px; max-height: 400px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: ${scrollThumbColor} ${scrollTrackColor}; transition: max-height 0.3s ease-out, padding 0.3s ease-out, opacity 0.25s ease-out;} .${RENDERED_INFO_BAR_CLASS} .infobar-display-content.collapsed { max-height: 0; padding-top: 0; padding-bottom: 0; opacity: 0; border-top: none; margin-top: 0px; overflow-y: hidden; } .${RENDERED_INFO_BAR_CLASS} .infobar-display-item, .${RENDERED_INFO_BAR_CLASS} .list-panel-item { padding: 5px 2px; display: flex; font-size:0.98em; border-bottom: 1px dotted ${theme.itemBorder}; line-height: 1.5; justify-content: space-between; align-items: flex-start; } .${RENDERED_INFO_BAR_CLASS} .infobar-display-item:last-child, .${RENDERED_INFO_BAR_CLASS} .list-panel-item:last-child { border-bottom: none; } .${RENDERED_INFO_BAR_CLASS} .infobar-display-label, .${RENDERED_INFO_BAR_CLASS} .list-panel-item strong { font-weight: 500; color: ${theme.itemLabelColor}; margin-right: 8px; flex-shrink:0; white-space: nowrap; align-self: flex-start; padding-top: 1px; text-align: left; } .${RENDERED_INFO_BAR_CLASS} .infobar-display-value, .${RENDERED_INFO_BAR_CLASS} .list-panel-item span:not(.progress-bar-container > span):not(.progress-bar) { color: ${theme.itemValueColor}; word-break: break-word; flex-grow: 1; text-align: left; } .${RENDERED_INFO_BAR_CLASS} .infobar-display-value ul, .${RENDERED_INFO_BAR_CLASS} .infobar-display-value ol { margin:0; padding-left: 18px; } .${RENDERED_INFO_BAR_CLASS} .infobar-display-value pre { margin:0; padding: 3px; background-color: ${isDark ? 'rgba(0,0,0,0.1)' : 'rgba(0,0,0,0.03)'}; border-radius:3px; } .${RENDERED_INFO_BAR_CLASS} .infobar-npc-selector-container { display: flex; align-items: center; margin-bottom: 8px; padding: 4px; background-color: rgba(128,128,128,0.1); border-radius: 4px;} .${RENDERED_INFO_BAR_CLASS} #${NPC_SELECTOR_ID} { padding: 4px 6px; border-radius: 3px; border: 1px solid ${theme.itemBorder}; background-color: ${theme.sectionBg}; color: ${theme.text}; font-size: 0.9em; } .${RENDERED_INFO_BAR_CLASS} .npc-details-wrapper { border-left: 3px solid ${theme.itemLabelColor}; padding-left: 8px; margin-top: 8px; } .${RENDERED_INFO_BAR_CLASS} .npc-details-wrapper .infobar-display-item {} .${RENDERED_INFO_BAR_CLASS} .complex-data div { margin-bottom: 3px; line-height: 1.4; text-align:left; } .${RENDERED_INFO_BAR_CLASS} .complex-data div:last-child { margin-bottom: 0; } .${RENDERED_INFO_BAR_CLASS} .complex-data strong { color: ${theme.titleColor}; font-weight: 500; margin-right: 5px; } .${RENDERED_INFO_BAR_CLASS} .task-data, .${RENDERED_INFO_BAR_CLASS} .shop-item-data, .${RENDERED_INFO_BAR_CLASS} .shareholders-list, .${RENDERED_INFO_BAR_CLASS} .projects-list, .${RENDERED_INFO_BAR_CLASS} .task-list-container, .${RENDERED_INFO_BAR_CLASS} .affection-list-container, .${RENDERED_INFO_BAR_CLASS} .warehouse-list-container, .${RENDERED_INFO_BAR_CLASS} .abilities-list, .${RENDERED_INFO_BAR_CLASS} .news-list-container, .${RENDERED_INFO_BAR_CLASS} .rivals-list { border-left: 3px solid ${theme.itemLabelColor}; padding-left: 8px; margin-top: 2px; margin-bottom: 2px; max-height: 150px; overflow-y: auto; scrollbar-width: thin; scrollbar-color: ${scrollThumbColor} ${scrollTrackColor};} .${RENDERED_INFO_BAR_CLASS} .task-list-container .sub-task-item, .${RENDERED_INFO_BAR_CLASS} .projects-list .project-item, .${RENDERED_INFO_BAR_CLASS} .abilities-list li, .${RENDERED_INFO_BAR_CLASS} .news-list-container .news-item-simple, .${RENDERED_INFO_BAR_CLASS} .rivals-list .rival-item { border-bottom: 1px dashed ${theme.itemBorder}; padding-bottom: 5px; margin-bottom: 5px; } .${RENDERED_INFO_BAR_CLASS} .task-list-container .sub-task-item:last-child, .${RENDERED_INFO_BAR_CLASS} .projects-list .project-item:last-child, .${RENDERED_INFO_BAR_CLASS} .abilities-list li:last-child, .${RENDERED_INFO_BAR_CLASS} .news-list-container .news-item-simple:last-child, .${RENDERED_INFO_BAR_CLASS} .rivals-list .rival-item:last-child { border-bottom: none; margin-bottom: 0; } .${RENDERED_INFO_BAR_CLASS} .shareholders-list, .${RENDERED_INFO_BAR_CLASS} .abilities-list { list-style: none; padding-left: 0px; } .${RENDERED_INFO_BAR_CLASS} .shareholders-list li { margin-bottom: 2px; padding-left: 5px; display:flex; justify-content:space-between; } .${RENDERED_INFO_BAR_CLASS} .shareholders-list li span:last-child { text-align: right; } .${RENDERED_INFO_BAR_CLASS} .abilities-list li, .${RENDERED_INFO_BAR_CLASS} .news-list-container .news-item-simple { margin-bottom: 3px; } .${RENDERED_INFO_BAR_CLASS} .abilities-list li small { display:block; margin-left:10px; opacity:0.8; } .${RENDERED_INFO_BAR_CLASS} .news-list-container .news-item-simple strong { display: block; margin-bottom: 2px; } .${RENDERED_INFO_BAR_CLASS} .infobar-display-item.complex-item .infobar-display-label { align-self: flex-start; padding-top: 3px; } .${RENDERED_INFO_BAR_CLASS} .projects-list::-webkit-scrollbar, .${RENDERED_INFO_BAR_CLASS} .shareholders-list::-webkit-scrollbar, .${RENDERED_INFO_BAR_CLASS} .task-list-container::-webkit-scrollbar, .${RENDERED_INFO_BAR_CLASS} .affection-list-container::-webkit-scrollbar, .${RENDERED_INFO_BAR_CLASS} .warehouse-list-container::-webkit-scrollbar, .${RENDERED_INFO_BAR_CLASS} .abilities-list::-webkit-scrollbar, .${RENDERED_INFO_BAR_CLASS} .news-list-container::-webkit-scrollbar, .${RENDERED_INFO_BAR_CLASS} .rivals-list::-webkit-scrollbar { width: 5px; } .${RENDERED_INFO_BAR_CLASS} .projects-list::-webkit-scrollbar-thumb, .${RENDERED_INFO_BAR_CLASS} .shareholders-list::-webkit-scrollbar-thumb, .${RENDERED_INFO_BAR_CLASS} .task-list-container::-webkit-scrollbar-thumb, .${RENDERED_INFO_BAR_CLASS} .affection-list-container::-webkit-scrollbar-thumb, .${RENDERED_INFO_BAR_CLASS} .warehouse-list-container::-webkit-scrollbar-thumb, .${RENDERED_INFO_BAR_CLASS} .abilities-list::-webkit-scrollbar-thumb, .${RENDERED_INFO_BAR_CLASS} .news-list-container::-webkit-scrollbar-thumb, .${RENDERED_INFO_BAR_CLASS} .rivals-list::-webkit-scrollbar-thumb { background-color: ${scrollThumbColor}; border-radius:3px; } .${RENDERED_INFO_BAR_CLASS} .affection-item { display: flex; align-items: center; justify-content: space-between; } .${RENDERED_INFO_BAR_CLASS} .affection-item strong { flex-basis: 120px; flex-shrink:0; text-align:left; margin-right:5px;} .${RENDERED_INFO_BAR_CLASS} .progress-bar-container { flex-grow:1; height: 12px; background-color: ${isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.08)'} ; border-radius: 6px; overflow: hidden; margin: 0 8px; border: 1px solid ${theme.itemBorder}; } .${RENDERED_INFO_BAR_CLASS} .progress-bar { height: 100%; transition: width 0.3s ease; text-align:center; font-size:9px; line-height:12px; color: white; text-shadow: 1px 1px 1px rgba(0,0,0,0.3); } .${RENDERED_INFO_BAR_CLASS} .affection-item > span:last-child { flex-basis: 100px; text-align: right; flex-shrink:0; white-space:nowrap; } .${RENDERED_INFO_BAR_CLASS} .warehouse-item strong { text-align:left; } .${RENDERED_INFO_BAR_CLASS} .warehouse-item span { text-align:right; } ${newsFeedCss}`; }, null, 'getRenderedInfoBarStyle')(); }\n\n\n    // --- Initialization ---\n    async function mainInitSequence() {\n        console.log('[信息栏UPM v2.3.69] Main initialization sequence starting...'); \n        try {\n            if (!window.parent || !window.parent.document) {\n                console.error(\"[信息栏UPM] Parent document not available. Retrying init sequence...\");\n                setTimeout(mainInitSequence, 1000); return;\n            }\n            \n            loadTheme();\n            await loadSettings(); \n            await updateChatVariablesFromSettings(); \n            createMenuButton(); \n            createBackupStatusWidget(); \n            \n            const initialContext = SillyTavern.getContext ? SillyTavern.getContext() : {};\n            previousCharacterFile = initialContext?.characterFilename || initialContext?.character_file || null;\n            \n            let currentChatDataForInit = await loadInfoBarDataFromChatVars() || {};\n            const defaultSummarySettings = INFO_CONFIG.panels.summary.items.reduce((acc, item) => {\n                if (item.type === 'toggle') acc[item.id] = item.defaultEnabled;\n                else if (item.type === 'number') acc[item.id] = parseInt(item.defaultDisplayValue, 10);\n                else if (item.id === 'lastBigSummaryTitle' || item.id === 'lastSmallSummaryTitle') acc[item.id] = \"\";\n                return acc;\n            }, { aiTurnCount: 0, lastBigSummaryMsgCount: 0, lastSmallSummaryMsgCount: 0 });\n\n            currentChatDataForInit.summary = { \n                ...defaultSummarySettings, \n                ...(currentChatDataForInit.summary || {}), \n                aiTurnCount: currentChatDataForInit.summary?.aiTurnCount || 0, \n                lastBigSummaryMsgCount: currentChatDataForInit.summary?.lastBigSummaryMsgCount || 0,\n                lastSmallSummaryMsgCount: currentChatDataForInit.summary?.lastSmallSummaryMsgCount || 0,\n                bigSummaryInterval: parseInt(currentChatDataForInit.summary?.bigSummaryInterval, 10) || defaultSummarySettings.bigSummaryInterval || 30,\n                smallSummaryInterval: parseInt(currentChatDataForInit.summary?.smallSummaryInterval, 10) || defaultSummarySettings.smallSummaryInterval || 10,\n                enableBigSummary: currentChatDataForInit.summary?.enableBigSummary !== undefined ? currentChatDataForInit.summary.enableBigSummary : defaultSummarySettings.enableBigSummary,\n                enableSmallSummary: currentChatDataForInit.summary?.enableSmallSummary !== undefined ? currentChatDataForInit.summary.enableSmallSummary : defaultSummarySettings.enableSmallSummary,\n                lastBigSummaryTitle: currentChatDataForInit.summary?.lastBigSummaryTitle || \"\",\n                lastSmallSummaryTitle: currentChatDataForInit.summary?.lastSmallSummaryTitle || \"\"\n            };\n\n\n            if (currentChatDataForInit && currentChatDataForInit['personal.name'] && !AI_PLACEHOLDERS.includes(String(currentChatDataForInit['personal.name']))) {\n                lastKnownCharNameFromContext = String(currentChatDataForInit['personal.name']);\n            } else {\n                 lastKnownCharNameFromContext = getDisplayCharacterName(); \n            }\n            await saveInfoBarDataToChatVars(currentChatDataForInit); \n            updateBackupPanelList(); \n            updateBackupStatusWidget(); \n            \n            eventOn(tavern_events.CHAT_CHANGED, errorCatched(async (eventData) => {\n                clearTimeout(chatChangeDebounceTimer); \n                chatChangeDebounceTimer = setTimeout(errorCatched(async () => {\n                    const currentContext = SillyTavern.getContext ? SillyTavern.getContext() : {};\n                    const newCharacterFile = currentContext?.characterFilename || currentContext?.character_file || null;\n                    \n                    if (newCharacterFile && newCharacterFile !== previousCharacterFile) {\n                        console.log(`[信息栏UPM INFO CHAT_CHANGED] Character file changed. Clearing chat variable.`);\n                        await clearCurrentCharInfoBarData(false); \n                        lastKnownCharNameFromContext = getDisplayCharacterName(); \n                        previousCharacterFile = newCharacterFile;                         \n                        let data = await loadInfoBarDataFromChatVars() || {};\n                        const sumPanelConfig = INFO_CONFIG.panels.summary; \n                        const panelSettings = currentSettings.summary || {};\n\n                        const defaultItems = {};\n                        if (sumPanelConfig && sumPanelConfig.items) {\n                           sumPanelConfig.items.forEach(item => {\n                                if (item.type === 'toggle') defaultItems[item.id] = item.defaultEnabled;\n                                else if (item.type === 'number') defaultItems[item.id] = parseInt(item.defaultDisplayValue, 10);\n                           });\n                        }\n\n\n                        data.summary = { \n                            aiTurnCount: 0, \n                            lastBigSummaryMsgCount: 0, \n                            lastSmallSummaryMsgCount: 0, \n                            bigSummaryInterval: panelSettings.itemsEnabled?.bigSummaryInterval !== undefined ? parseInt(panelSettings.itemsEnabled.bigSummaryInterval, 10) : (defaultItems.bigSummaryInterval || 30), \n                            smallSummaryInterval: panelSettings.itemsEnabled?.smallSummaryInterval !== undefined ? parseInt(panelSettings.itemsEnabled.smallSummaryInterval, 10) : (defaultItems.smallSummaryInterval || 10), \n                            enableBigSummary: panelSettings.itemsEnabled?.enableBigSummary !== undefined ? panelSettings.itemsEnabled.enableBigSummary : (defaultItems.enableBigSummary !== undefined ? defaultItems.enableBigSummary : true), \n                            enableSmallSummary: panelSettings.itemsEnabled?.enableSmallSummary !== undefined ? panelSettings.itemsEnabled.enableSmallSummary : (defaultItems.enableSmallSummary !== undefined ? defaultItems.enableSmallSummary : true),  \n                            lastBigSummaryTitle: \"\", \n                            lastSmallSummaryTitle: \"\"  \n                        };\n                        await saveInfoBarDataToChatVars(data);\n                    } else if (!newCharacterFile && previousCharacterFile) {\n                        console.log(`[信息栏UPM INFO CHAT_CHANGED] Character file became null. Clearing chat variable.`);\n                        await clearCurrentCharInfoBarData(false);\n                        lastKnownCharNameFromContext = \"(未知角色)\";\n                        previousCharacterFile = null;\n                    }\n                    \n                    const currentDisplayName = getDisplayCharacterName();\n                    if(lastKnownCharNameFromContext !== currentDisplayName && currentDisplayName !== \"(未知角色)\") {\n                        lastKnownCharNameFromContext = currentDisplayName;\n                    }\n                    \n                    await loadSettings(); \n                    const currentChatData = await loadInfoBarDataFromChatVars() || {}; \n                    if (currentChatData && currentChatData['personal.name'] && !AI_PLACEHOLDERS.includes(String(currentChatData['personal.name']))) {\n                        lastKnownCharNameFromContext = String(currentChatData['personal.name']);\n                    } else if (lastKnownCharNameFromContext === \"(未知角色)\") { \n                        lastKnownCharNameFromContext = getDisplayCharacterName();\n                    }\n\n                    updateBackupPanelList(); \n                    updateBackupStatusWidget(); \n                }, null, 'chatChangedDebouncedCallback'), 350); \n            }, null, 'onChatChanged'));\n            \n            if (typeof MutationObserver === 'function' && window.parent && window.parent.document.body) {\n                const bodyObserver = new MutationObserver(errorCatched(() => {\n                    if (currentTheme === 'auto') { /* ... auto theme logic ... */ }\n                }, null, 'bodyThemeObserver'));\n                bodyObserver.observe(window.parent.document.body, { attributes: true, attributeFilter: ['class'] });\n            }\n\n            eventOn(tavern_events.CHARACTER_MESSAGE_RENDERED, errorCatched(async (mID)=>{await handleMessageRendering(mID)}, null, 'onCharMsgRendered')); // Corrected syntax\n            eventOn(tavern_events.MESSAGE_UPDATED, errorCatched(async(mID)=>{ const $mN=retrieveDisplayedMessage(mID); if($mN&&$mN.length>0&&$mN.attr('is_user')==='false') await handleMessageRendering(mID); }, null, 'onMsgUpdated'));\n            \n            console.log('[信息栏UPM v2.3.69] Main initialization sequence complete.'); \n        } catch (initError) {\n            console.error(\"[信息栏UPM CRITICAL INIT ERROR]:\", initError, initError.stack);\n            alert(\"[信息栏UPM] 初始化时发生严重错误，部分功能可能无法使用。请检查浏览器控制台获取详细信息。\");\n        }\n    }\n\n    if (typeof tavern_events !== 'undefined' && tavern_events.APP_READY && typeof eventOn === 'function') {\n        eventOn(tavern_events.APP_READY, () => {\n            console.log(\"[信息栏UPM INFO] APP_READY event received. Initializing UPM script.\");\n            mainInitSequence();\n        });\n    } else if (typeof $ === 'function') { \n         $(document).ready(() => {\n            console.log(\"[信息栏UPM INFO] Document ready. Attempting UPM script initialization.\");\n            mainInitSequence();\n        });\n    } else {\n        console.error(\"[信息栏UPM FATAL] Critical environment components (SillyTavern events or jQuery) not found. Cannot initialize.\");\n        alert(\"[信息栏UPM] 脚本初始化失败：缺少必要的运行环境。\");\n    }\n\n})();\n", "info": "", "buttons": []}
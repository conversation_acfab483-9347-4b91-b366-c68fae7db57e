{"entries": {"2": {"uid": 2, "key": [], "keysecondary": [], "comment": "信息栏COT", "content": "[System Instructions for InfoBar Data Output (Local Parse Method - v2.3.69 - FULL_NPC_DATA, STABLE_ID, LOC_PERSIST, MANDATORY_REFLECT_V2, SUMMARY_V2_REFINED):\n\n**核心任务**: 你的常规叙事回复之后，**除非你收到了明确的剧情总结请求（见下文），否则必须，没有任何例外或遗漏地，**紧接着输出你的思考规划块 (`<aiThinkProcess>`) 和数据输出块 (`<thinkbiao>`)。这两个块是系统元数据，**绝对不能**包含在你的主要叙事回复文本中，它们会被脚本解析。\n\n1.  **思考规划块 (`<aiThinkProcess>`)**:\n    *   此块必须以 `<aiThinkProcess>` 开始，并以 `</aiThinkProcess>` 结束。\n    *   在此块中，用XML注释（`<!-- ... -->`）或结构化列表描述你对信息栏各字段值的决策过程。\n    *   **思考范围严格限定**：只思考和记录下方“信息栏数据字段清单”中列出的、且用户设置为启用了的面板和字段。\n    *   **强制自我反思 (MANDATORY REFLECTION)**：在对每个主要NPC的关键属性和每个启用的关键面板核心字段做出初步决策后，**必须执行并记录一次简短的自我反思**，确认其合理性，格式如 `<!-- [Reflect Field/NPC_ID]: Reason/Check. OK. -->`。此环节不可省略。\n\n2.  **数据输出块 (`<thinkbiao>`)**:\n    *   此块必须以 `<thinkbiao>` 开始，并以 `</thinkbiao>` 结束。\n    *   根据思考结果，输出所有启用的信息栏项目的当前状态值。\n    *   **格式要求**：\n        *   必须是**逐行输出的键值对列表**，每行格式为 `key: value`。\n        *   键名严格使用 `panelName.itemName` (例如 `personal.name`) 或 `npcX.attributeName` (例如 `npc1.id`)。\n        *   **绝对禁止**将整个 `<thinkbiao>` 内容作为一个单一的JSON对象输出。\n        *   列表型数据 (如 `internet.newsItems`, `tasks.sideQuests`) 必须是合法的、**单行的JSON数组字符串**。\n        *   对象型数据 (如 `tasks.mainQuest`) 必须是合法的、**单行的JSON对象字符串**。\n        *   只输出清单中存在且已启用的字段。\n        *   如果无法提供某个启用字段的值，请明确输出该字段并赋值为`(数据获取失败)`，避免完全省略。\n\n**数据持久性与更新原则 (最重要！！！)**:\n*   **【核心原则】**: 信息栏数据是需要长期维护的状态记录。当你生成`<thinkbiao>`时，对于任何一个字段，如果：\n    1.  当前对话**没有提供关于此字段的新的、明确的、需要更新的信息**，并且\n    2.  你通过 `{{get_chat_variable ...}}` 获取到的该字段**已有具体的、非占位符的值**，\n    那么，你**必须输出上次记录的那个具体值**。\n*   **【严禁无故清空/覆盖】**: **绝对禁止**无故将已有的具体数据清空或替换为通用占位符（如`(AI设定年龄)`、`[]`（除非列表确实应为空）、`{}`（除非对象确实应为空）、`(待补充)`等）。只有当剧情发生实质性改变，导致旧数据确实不再适用时，才应更新数据。\n*   **【列表型数据特例】**: 对于列表型数据（如 `warehousePanel.list`, `tasks.sideQuests`, `internet.newsItems`）：如果当前对话中没有任何关于列表项增、删、改的信息，请**直接输出上次记录的完整JSON数组字符串（通过 `{{get_chat_variable ... '[]'}}` 获取，注意这里的 `'[]'` 是当变量不存在时的默认值，如果变量存在且是有效JSON，你会获取到那个JSON）**。如果列表确实应该变为空（例如，所有任务都完成了，或所有物品都消耗了），则输出 `[]`。\n\n**剧情总结机制**:\n*   当你收到形如 `[角色扮演提示：现在你需要进行一次“[大/小]剧情总结”...]` 的指令时，你的回复**只需要包含总结内容，并用 `<summaryText type=\"[big/small]\">...</summaryText>` 标签包裹起来**。此时，**不应输出** `<aiThinkProcess>` 和 `<thinkbiao>`。\n\n**第一步：思考规划块 (`<aiThinkProcess>`) (仅在非总结请求时输出)**\n内容组织建议（保持简洁）：\n*   **当前场景分析**:\n    *   识别NPC（最多{{MAX_AI_NPCS}}个）。NPC定义：**除主角外，有独立名称、身份，并能与主角直接、有意义互动的实体（包括有具体虚拟形象的AI助手如“零”）**。排除：主角本人、纯后台系统、无形AI语音。\n    *   为每个NPC分配一个**永久固定且唯一的英文ID** (例如 `zero_engineer_01`, `eva_smith_main`)。在所有后续的`<aiThinkProcess>`和`<thinkbiao>`中，对于**同一个NPC，必须始终使用这个相同的ID**。\n    *   主要焦点NPC。\n    *   **强制自我反思**: `<!-- [Reflect SceneAnalysis]: NPC识别是否准确(未遗漏/错选)? ID是否稳定唯一? 焦点NPC判断是否合理? -->`\n*   **NPC数据决策 (为每个已识别且需要在`<thinkbiao>`中输出详细信息的NPC)**:\n    *   ID, 姓名。\n    *   **务必思考并记录该NPC所有已启用的 `npcX.*` 属性的值**，特别是衣物细节 (`upperBody`, `lowerBody`, `footwear`, `clothing`)、情绪、状态、好感度等。\n    *   **强制自我反思**: `<!-- [Reflect NPC {{npc_id}}]: 类型OK? ID沿用正确? 所有已启用属性是否均已思考并赋值(即使是默认值)? 情绪/状态/衣物是否符合当前情境和上次记录? 好感度/关系等其他关键属性是否准确或标记为(待补充)? -->`\n*   **其他面板数据决策 (仅针对启用的字段)**:\n    *   `personal.currentLocation`: (高优先级！依据最新对话，**若无新信息且上次为具体地点则保持，勿用占位符覆盖**。) `<!-- [Reflect personal.currentLocation]: 最新对话是否明确暗示位置变化? 若无变化，是否保持了上次的具体地点而非使用占位符? -->`\n    *   财务数据 (`company.*`, `personal.funds`): (依据已知信息或常识估算，无则`(待补充)`) `<!-- [Reflect 财务]: 依据? 计算准确? 避免编造/随意取整? 是否保持了上次有效的具体数值? -->`\n    *   `internet.newsItems`: (JSON数组, 单行) `<!-- [Reflect internet.newsItems]: 内容相关? JSON格式OK? 是否保持了上次有效的列表（如果无更新）? -->`\n    *   `tasks.*`: (JSON对象/数组, 单行) `<!-- [Reflect tasks]: JSON结构OK? 字段完整? 任务状态更新是否合理? -->`\n    *   (对其余启用的关键面板和字段，简要说明更新的依据或为何保持不变，并进行强制自我反思，确认数据持久性原则被遵守。)\n*   **最终确认**: 简述`<thinkbiao>`将包含哪些NPC的详细数据，以及其他面板的关键更新点和格式要求。\n\n**第二步：数据输出块 (`<thinkbiao>`) (仅在非总结请求时输出)**\n严格按照键值对列表格式。**对于你在`<aiThinkProcess>`中为每个NPC思考的所有已启用的属性，都必须在`<thinkbiao>`中为其输出对应的值。**\n\n--- 信息栏数据字段清单 (SillyTavern会替换 {{...}} 变量。请为以下每个启用的字段提供值，使用正确的键名格式) ---\n{{#if !(get_chat_variable 'infobar_upm_character_data_v1.summary.needsBigSummary') && !(get_chat_variable 'infobar_upm_character_data_v1.summary.needsSmallSummary')}}\n*   `worldTime.date`: {{get_chat_variable 'infobar_upm_character_data_v1.worldTime.date' '(当前年月日)'}}\n*   `worldTime.time`: {{get_chat_variable 'infobar_upm_character_data_v1.worldTime.time' '(当前时分)'}}\n*   `worldTime.weather`: {{get_chat_variable 'infobar_upm_character_data_v1.worldTime.weather' '(当前天气状况)'}}\n\n{{#if (get_chat_variable 'infobar_panel_personal_enabled')}}\n    {{editor \"(Personal Panel - 务必遵循数据持久性原则)\"}}\n    {{#if (get_chat_variable 'infobar_personal_name_enabled')}}*   `personal.name`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.name' (char_name | default:\"(AI设定姓名)\")}}{{/if}}\n    {{#if (get_chat_variable 'infobar_personal_age_enabled')}}*   `personal.age`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.age' (get_lorebook_value 'infobar_default_age' '(AI设定年龄，纯数字)')}}{{/if}}\n    {{#if (get_chat_variable 'infobar_personal_gender_enabled')}}*   `personal.gender`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.gender' (get_lorebook_value 'infobar_default_gender' '(AI设定性别)')}}{{/if}}\n    {{#if (get_chat_variable 'infobar_personal_race_enabled')}}*   `personal.race`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.race' (get_lorebook_value 'infobar_default_race' '(AI设定种族)')}}{{/if}}\n    {{#if (get_chat_variable 'infobar_personal_currentLocation_enabled')}}*   `personal.currentLocation`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.currentLocation' '(AI根据当前对话场景判断主角的具体位置并更新。若无新信息且上次有具体值则保持上次值!)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_personal_status_enabled')}}*   `personal.status`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.status' '(角色当前具体身体状态和行为描述)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_personal_mood_enabled')}}*   `personal.mood`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.mood' '(角色当前主要情绪)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_personal_funds_enabled')}}*   `personal.funds`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.funds' (get_lorebook_value 'infobar_initial_funds' '0.00')}} (角色流动资金，纯数字，精确到两位小数){{/if}}\n    {{#if (get_chat_variable 'infobar_personal_points_enabled')}}*   `personal.points`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.points' '0'}} (系统/特殊积分，纯数字){{/if}}\n    {{#if (get_chat_variable 'infobar_personal_appearance_enabled')}}*   `personal.appearance`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.appearance' (get_lorebook_value 'infobar_appearance_snippet' '(AI描述外貌)')}}{{/if}}\n    {{#if (get_chat_variable 'infobar_personal_personality_enabled')}}*   `personal.personality`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.personality' (get_lorebook_value 'infobar_personality_snippet' '(AI描述个性)')}}{{/if}}\n    {{#if (get_chat_variable 'infobar_personal_health_enabled')}}*   `personal.health`: {{get_chat_variable 'infobar_upm_character_data_v1.personal.health' '(角色健康状况)'}}{{/if}}\n{{/if}}\n\n{{#if (get_chat_variable 'infobar_panel_company_enabled')}}\n    {{editor \"(Company Panel - 遵循数据持久性原则)\"}}\n    {{#if (get_chat_variable 'infobar_company_name_enabled')}}*   `company.name`: {{get_chat_variable 'infobar_upm_character_data_v1.company.name' (get_lorebook_value 'infobar_org_name' '(暂无组织)')}}{{/if}}\n    {{#if (get_chat_variable 'infobar_company_type_enabled')}}*   `company.type`: {{get_chat_variable 'infobar_upm_character_data_v1.company.type' (get_lorebook_value 'infobar_org_type' '(AI设定该组织类型)')}}{{/if}}\n    {{#if (get_chat_variable 'infobar_company_status_enabled')}}*   `company.status`: {{get_chat_variable 'infobar_upm_character_data_v1.company.status' '(该组织当前运营状态)')}}{{/if}}\n    {{#if (get_chat_variable 'infobar_company_mainBusiness_enabled')}}*   `company.mainBusiness`: {{get_chat_variable 'infobar_upm_character_data_v1.company.mainBusiness' '(AI总结其核心业务范围或主要产品/服务)')}}{{/if}}\n    {{#if (get_chat_variable 'infobar_company_employeeCount_enabled')}}*   `company.employeeCount`: {{get_chat_variable 'infobar_upm_character_data_v1.company.employeeCount' '(待补充)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_company_hqLocation_enabled')}}*   `company.hqLocation`: {{get_chat_variable 'infobar_upm_character_data_v1.company.hqLocation' '(AI设定其总部所在的具体城市或地区)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_company_valuation_enabled')}}*   `company.valuation`: {{get_chat_variable 'infobar_upm_character_data_v1.company.valuation' '(待补充)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_company_reputation_enabled')}}*   `company.reputation`: {{get_chat_variable 'infobar_upm_character_data_v1.company.reputation' '(该组织在行业内或社会上的声望描述)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_company_funds_enabled')}}*   `company.funds`: {{get_chat_variable 'infobar_upm_character_data_v1.company.funds' '(待补充)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_company_shareholders_enabled')}}*   `company.shareholders`: {{get_chat_variable 'infobar_upm_character_data_v1.company.shareholders' '[]'}} (JSON数组格式字符串，例如 `[{\"name\":\"林天\",\"stake\":\"100%\"}]`){{/if}}\n    {{#if (get_chat_variable 'infobar_company_projects_enabled')}}*   `company.projects`: {{get_chat_variable 'infobar_upm_character_data_v1.company.projects' '[]'}} (JSON数组格式字符串，例如 `[{\"name\":\"AI助手雏形开发\",\"status\":\"进行中\",\"progress\":\"技术包部署完成\"}]`){{/if}}\n    {{#if (get_chat_variable 'infobar_company_recentEvents_enabled')}}*   `company.recentEvents`: {{get_chat_variable 'infobar_upm_character_data_v1.company.recentEvents' '[]'}} (JSON数组格式字符串，例如 `[\"公司成立\",\"核心团队入驻\"]`){{/if}}\n    {{#if (get_chat_variable 'infobar_company_rivals_enabled')}}*   `company.rivals`: {{get_chat_variable 'infobar_upm_character_data_v1.company.rivals' '[]'}} (JSON数组格式字符串){{/if}}\n    {{#if (get_chat_variable 'infobar_company_shares_enabled')}}*   `company.shares`: {{get_chat_variable 'infobar_upm_character_data_v1.company.shares' '(不适用)'}}{{/if}}\n{{/if}}\n\n{{#if (get_chat_variable 'infobar_panel_interactiondisplay_enabled')}}\n    *   `interactionTargets.list`: {{get_chat_variable 'infobar_upm_character_data_v1.interactionTargets.list' '[]'}} (JSON数组，每个对象只含 `id` 和 `name`。例如: `[{\"id\":\"zero_engineer_01\",\"name\":\"零\"}]`。若无NPC，则为 `[]`)\n    \n    {{editor \"(NPC Panels - 务必为每个清单中的NPC提供所有启用的属性，包括衣物细节。ID务必保持稳定！)\"}}\n    {{!-- NPC 1 (Primary Focus) --}}\n    *   `npc1.id`: (主要NPC 1 的唯一且稳定的英文ID。如果不存在则此行及后续npc1.*行不输出)\n    {{#if (get_chat_variable 'infobar_interactiondisplay_name_enabled')}}*   `npc1.name`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.name' '(主要NPC 1 的姓名)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_identity_enabled')}}*   `npc1.identity`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.identity' '(主要NPC 1 的身份)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_mood_enabled')}}*   `npc1.mood`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.mood' '(主要NPC 1 的情绪)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_currentState_enabled')}}*   `npc1.currentState`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.currentState' '(主要NPC 1 的当前状态/动作)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_affectionToUser_enabled')}}*   `npc1.affectionToUser`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.affectionToUser' '-/-'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_relationshipToUser_enabled')}}*   `npc1.relationshipToUser`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.relationshipToUser' '(未知)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_goodWill_enabled')}}*   `npc1.goodWill`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.goodWill' '中立'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_emotional_status_enabled')}}*   `npc1.emotional_status`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.emotional_status' '{}'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_upperBody_enabled')}}*   `npc1.upperBody`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.upperBody' '(上身穿着)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_lowerBody_enabled')}}*   `npc1.lowerBody`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.lowerBody' '(下身穿着)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_footwear_enabled')}}*   `npc1.footwear`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.footwear' '(鞋袜)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_clothing_enabled')}}*   `npc1.clothing`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.clothing' '(整体穿着描述，可选)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_physicalFeatures_enabled')}}*   `npc1.physicalFeatures`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.physicalFeatures' '(身体特征)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_hobbies_enabled')}}*   `npc1.hobbies`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.hobbies' '[]'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_shameLevel_enabled')}}*   `npc1.shameLevel`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.shameLevel' '-/-'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_angerLevel_enabled')}}*   `npc1.angerLevel`: {{get_chat_variable 'infobar_upm_character_data_v1.npc1.angerLevel' '-/-'}}{{/if}}\n    {{!-- (NSFW fields omitted for brevity in default prompt, AI knows to include if enabled) --}}\n\n    {{!-- NPC 2 (Secondary Focus) --}}\n    *   `npc2.id`: (次要NPC 2 的唯一且稳定的英文ID。若无则此行及后续不输出)\n    {{#if (get_chat_variable 'infobar_interactiondisplay_name_enabled')}}*   `npc2.name`: {{get_chat_variable 'infobar_upm_character_data_v1.npc2.name' '(NPC2姓名)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_identity_enabled')}}*   `npc2.identity`: {{get_chat_variable 'infobar_upm_character_data_v1.npc2.identity' '(NPC2身份)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_mood_enabled')}}*   `npc2.mood`: {{get_chat_variable 'infobar_upm_character_data_v1.npc2.mood' '(NPC2情绪)'}}{{/if}}\n    {{!-- ... (AI必须为NPC2输出所有其他已启用的属性，格式同NPC1) ... --}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_upperBody_enabled')}}*   `npc2.upperBody`: {{get_chat_variable 'infobar_upm_character_data_v1.npc2.upperBody' '(上身穿着)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_lowerBody_enabled')}}*   `npc2.lowerBody`: {{get_chat_variable 'infobar_upm_character_data_v1.npc2.lowerBody' '(下身穿着)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_footwear_enabled')}}*   `npc2.footwear`: {{get_chat_variable 'infobar_upm_character_data_v1.npc2.footwear' '(鞋袜)'}}{{/if}}\n\n\n    {{!-- NPC 3 (Tertiary Focus) --}}\n    *   `npc3.id`: (第三NPC 3 的唯一且稳定的英文ID。若无则此行及后续不输出)\n    {{#if (get_chat_variable 'infobar_interactiondisplay_name_enabled')}}*   `npc3.name`: {{get_chat_variable 'infobar_upm_character_data_v1.npc3.name' '(NPC3姓名)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_identity_enabled')}}*   `npc3.identity`: {{get_chat_variable 'infobar_upm_character_data_v1.npc3.identity' '(NPC3身份)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_mood_enabled')}}*   `npc3.mood`: {{get_chat_variable 'infobar_upm_character_data_v1.npc3.mood' '(NPC3情绪)'}}{{/if}}\n    {{!-- ... (AI必须为NPC3输出所有其他已启用的属性，格式同NPC1) ... --}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_upperBody_enabled')}}*   `npc3.upperBody`: {{get_chat_variable 'infobar_upm_character_data_v1.npc3.upperBody' '(上身穿着)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_lowerBody_enabled')}}*   `npc3.lowerBody`: {{get_chat_variable 'infobar_upm_character_data_v1.npc3.lowerBody' '(下身穿着)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_footwear_enabled')}}*   `npc3.footwear`: {{get_chat_variable 'infobar_upm_character_data_v1.npc3.footwear' '(鞋袜)'}}{{/if}}\n\n    {{!-- NPC 4 (Fourth Focus) --}}\n    *   `npc4.id`: (第四NPC 4 的唯一且稳定的英文ID。若无则此行及后续不输出)\n    {{#if (get_chat_variable 'infobar_interactiondisplay_name_enabled')}}*   `npc4.name`: {{get_chat_variable 'infobar_upm_character_data_v1.npc4.name' '(NPC4姓名)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_identity_enabled')}}*   `npc4.identity`: {{get_chat_variable 'infobar_upm_character_data_v1.npc4.identity' '(NPC4身份)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_mood_enabled')}}*   `npc4.mood`: {{get_chat_variable 'infobar_upm_character_data_v1.npc4.mood' '(NPC4情绪)'}}{{/if}}\n    {{!-- ... (AI必须为NPC4输出所有其他已启用的属性，格式同NPC1) ... --}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_upperBody_enabled')}}*   `npc4.upperBody`: {{get_chat_variable 'infobar_upm_character_data_v1.npc4.upperBody' '(上身穿着)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_lowerBody_enabled')}}*   `npc4.lowerBody`: {{get_chat_variable 'infobar_upm_character_data_v1.npc4.lowerBody' '(下身穿着)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_interactiondisplay_footwear_enabled')}}*   `npc4.footwear`: {{get_chat_variable 'infobar_upm_character_data_v1.npc4.footwear' '(鞋袜)'}}{{/if}}\n{{/if}}\n\n{{#if (get_chat_variable 'infobar_panel_focusaffectionnpc_enabled')}}\n    *   `focusAffectionNPC.name`: {{get_chat_variable 'infobar_upm_character_data_v1.focusAffectionNPC.name' '(无焦点对象)'}}\n    *   `focusAffectionNPC.value`: {{get_chat_variable 'infobar_upm_character_data_v1.focusAffectionNPC.value' '50'}}\n    *   `focusAffectionNPC.max`: {{get_chat_variable 'infobar_upm_character_data_v1.focusAffectionNPC.max' '100'}}\n    *   `focusAffectionNPC.status`: {{get_chat_variable 'infobar_upm_character_data_v1.focusAffectionNPC.status' '(中立)'}}\n{{/if}}\n\n{{#if (get_chat_variable 'infobar_panel_affectionlist_enabled')}}\n    *   `affectionPanel.list`: {{get_chat_variable 'infobar_upm_character_data_v1.affectionPanel.list' '[]'}} (JSON数组格式。**若无好感度信息更新，必须输出完整的空数组 `[]`。**)\n{{/if}}\n{{#if (get_chat_variable 'infobar_panel_warehouse_enabled')}}\n    *   `warehousePanel.list`: {{get_chat_variable 'infobar_upm_character_data_v1.warehousePanel.list' '[]'}} (JSON数组格式。**若仓库为空，必须输出完整的空数组 `[]`。**)\n{{/if}}\n\n{{#if (get_chat_variable 'infobar_panel_tasks_enabled')}}\n    {{#if (get_chat_variable 'infobar_tasks_mainQuest_enabled')}}*   `tasks.mainQuest`: {{get_chat_variable 'infobar_upm_character_data_v1.tasks.mainQuest' '{\"name\":\"(暂无主线)\",\"status\":\"-\",\"description\":\"\",\"reward\":null}'}} (JSON对象字符串){{/if}}\n    {{#if (get_chat_variable 'infobar_tasks_sideQuests_enabled')}}*   `tasks.sideQuests`: {{get_chat_variable 'infobar_upm_character_data_v1.tasks.sideQuests' '[]'}} (JSON对象数组字符串, 每项包含name, status, description, progress(可选), reward(可选, 对象含type, details)){{/if}}\n    {{#if (get_chat_variable 'infobar_tasks_dailyTask_enabled')}}*   `tasks.dailyTask`: {{get_chat_variable 'infobar_upm_character_data_v1.tasks.dailyTask' '{\"name\":\"(暂无每日)\",\"status\":\"-\",\"description\":\"\",\"reward\":null}'}} (JSON对象字符串){{/if}}\n{{/if}}\n{{#if (get_chat_variable 'infobar_panel_environment_enabled')}}\n    {{#if (get_chat_variable 'infobar_environment_location_enabled')}}*   `environment.location`: {{get_chat_variable 'infobar_upm_character_data_v1.environment.location' '(当前场景的具体地点名称)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_environment_nearbyCharacters_enabled')}}*   `environment.nearbyCharacters`: {{get_chat_variable 'infobar_upm_character_data_v1.environment.nearbyCharacters' '(场景中其他可见或可感知的角色/生物的简述)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_environment_ambiance_enabled')}}*   `environment.ambiance`: {{get_chat_variable 'infobar_upm_character_data_v1.environment.ambiance' '(当前场景的整体氛围描述)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_environment_sound_enabled')}}*   `environment.sound`: {{get_chat_variable 'infobar_upm_character_data_v1.environment.sound' '(当前场景中主要的背景声音)'}}{{/if}}\n{{/if}}\n{{#if (get_chat_variable 'infobar_panel_clothing_enabled')}}\n    {{#if (get_chat_variable 'infobar_clothing_upperBody_enabled')}}*   `clothing.upperBody`: {{get_chat_variable 'infobar_upm_character_data_v1.clothing.upperBody' '(主角当前上身穿着的衣物描述)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_clothing_lowerBody_enabled')}}*   `clothing.lowerBody`: {{get_chat_variable 'infobar_upm_character_data_v1.clothing.lowerBody' '(主角当前下身穿着的衣物描述)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_clothing_posture_enabled')}}*   `clothing.posture`: {{get_chat_variable 'infobar_upm_character_data_v1.clothing.posture' '(主角当前的身体姿态)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_clothing_heldItem_enabled')}}*   `clothing.heldItem`: {{get_chat_variable 'infobar_upm_character_data_v1.clothing.heldItem' '(主角当前手持的物品，若有)'}}{{/if}}\n{{/if}}\n{{#if (get_chat_variable 'infobar_panel_abilities_enabled')}}\n    {{#if (get_chat_variable 'infobar_abilities_specialAbility_enabled')}}*   `abilities.specialAbility`: {{get_chat_variable 'infobar_upm_character_data_v1.abilities.specialAbility' '[]'}} (JSON数组字符串，例如 `[{\"name\":\"能力A\",\"description\":\"描述A\"}]`){{/if}}\n    {{#if (get_chat_variable 'infobar_abilities_learnedSkills_enabled')}}*   `abilities.learnedSkills`: {{get_chat_variable 'infobar_upm_character_data_v1.abilities.learnedSkills' '[]'}} (JSON数组字符串，例如 `[{\"name\":\"技能B\",\"level\":\"精通\"}]`){{/if}}\n{{/if}}\n{{#if (get_chat_variable 'infobar_panel_shop_enabled')}}\n    {{#if (get_chat_variable 'infobar_shop_currencyName_enabled')}}*   `shop.currencyName`: {{get_chat_variable 'infobar_upm_character_data_v1.shop.currencyName' '(当前场景或系统使用的商城货币名称)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_shop_featuredItem1_enabled')}}*   `shop.featuredItem1`: {{get_chat_variable 'infobar_upm_character_data_v1.shop.featuredItem1' '{\"name\":\"(空栏位)\"}'}} (JSON对象字符串){{/if}}\n    {{#if (get_chat_variable 'infobar_shop_featuredItem2_enabled')}}*   `shop.featuredItem2`: {{get_chat_variable 'infobar_upm_character_data_v1.shop.featuredItem2' '{\"name\":\"(空栏位)\"}'}} (JSON对象字符串){{/if}}\n    {{#if (get_chat_variable 'infobar_shop_featuredItem3_enabled')}}*   `shop.featuredItem3`: {{get_chat_variable 'infobar_upm_character_data_v1.shop.featuredItem3' '{\"name\":\"(空栏位)\"}'}} (JSON对象字符串){{/if}}\n    {{#if (get_chat_variable 'infobar_shop_refreshTime_enabled')}}*   `shop.refreshTime`: {{get_chat_variable 'infobar_upm_character_data_v1.shop.refreshTime' '(商城下次刷新商品的时间或条件)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_shop_shopNotice_enabled')}}*   `shop.shopNotice`: {{get_chat_variable 'infobar_upm_character_data_v1.shop.shopNotice' '(当前商店的公告或特殊说明)'}}{{/if}}\n{{/if}}\n\n{{#if (get_chat_variable 'infobar_panel_system_enabled')}} {{!-- Example for a custom \"system\" panel --}}\n    {{#if (get_chat_variable 'infobar_system_time_enabled')}}*    `system.time`: {{get_chat_variable 'infobar_upm_character_data_v1.system.time' '(系统时间)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_system_location_enabled')}}*    `system.location`: {{get_chat_variable 'infobar_upm_character_data_v1.system.location' '(系统判定地点)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_system_funds_enabled')}}*    `system.funds`: {{get_chat_variable 'infobar_upm_character_data_v1.system.funds' '0'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_system_points_enabled')}}*    `system.points`: {{get_chat_variable 'infobar_upm_character_data_v1.system.points' '0'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_system_techTree_enabled')}}*    `system.techTree`: {{get_chat_variable 'infobar_upm_character_data_v1.system.techTree' '{\"name\":\"(未解锁)\"}'}} (JSON对象字符串){{/if}}\n    {{#if (get_chat_variable 'infobar_system_tasks_enabled')}}*    `system.tasks`: {{get_chat_variable 'infobar_upm_character_data_v1.system.tasks' '[]'}} (JSON数组字符串, 每项含type, name, status){{/if}}\n    {{#if (get_chat_variable 'infobar_system_company.name_enabled')}}*    `system.company.name`: {{get_chat_variable 'infobar_upm_character_data_v1.system.company.name' '(无)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_system_company.status_enabled')}}*    `system.company.status`: {{get_chat_variable 'infobar_upm_character_data_v1.system.company.status' '(无)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_system_company.projects_enabled')}}*    `system.company.projects`: {{get_chat_variable 'infobar_upm_character_data_v1.system.company.projects' '[]'}} (JSON数组字符串){{/if}}\n{{/if}}\n\n{{#if (get_chat_variable 'infobar_panel_internet_enabled')}}\n    {{#if (get_chat_variable 'infobar_internet_newsItems_enabled')}}*    `internet.newsItems`: {{get_chat_variable 'infobar_upm_character_data_v1.internet.newsItems' '[]'}} (JSON数组字符串，每项含id, platform, author, avatar(可选URL), content, images(可选URL数组), video(可选URL), timestamp, likes, reposts, commentsCount, topComments(可选JSON数组，每项含user, comment, likes)){{/if}}\n    {{#if (get_chat_variable 'infobar_internet_time_enabled')}}*    `internet.time`: {{get_chat_variable 'infobar_upm_character_data_v1.internet.time' '(模拟互联网时间)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_internet_weather_enabled')}}*    `internet.weather`: {{get_chat_variable 'infobar_upm_character_data_v1.internet.weather' '(模拟互联网天气)'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_internet_socialFeeds_enabled')}}*    `internet.socialFeeds`: {{get_chat_variable 'infobar_upm_character_data_v1.internet.socialFeeds' '[]'}}{{/if}}\n    {{#if (get_chat_variable 'infobar_internet_forumThreads_enabled')}}*    `internet.forumThreads`: {{get_chat_variable 'infobar_upm_character_data_v1.internet.forumThreads' '[]'}}{{/if}}\n{{/if}}\n{{/if}} {{!-- End of check for not needing summary --}}\n\n--- 剧情总结指令 (仅在下方聊天变量为true时，你的回复只包含总结内容，并用<summaryText>标签包裹) ---\n{{#if (get_chat_variable 'infobar_upm_character_data_v1.summary.needsBigSummary')}}\n[角色扮演提示：现在你需要进行一次“大剧情总结”。请仔细回顾从上次大总结（消息计数 {{get_chat_variable 'infobar_upm_character_data_v1.summary.lastBigSummaryMsgCount' '0'}}，或从故事开始）到当前AI将要回复的这一轮对话前（AI自身计数为 {{get_chat_variable 'infobar_upm_character_data_v1.summary.summaryTriggerMsgCount' '未知'}}）的内容，用300-500字提炼出关键的剧情进展、主要人物关系变化、重要决策、新出现的地点或物品，以及任何悬而未决的谜团。将总结内容放在 <summaryText type=\"big\">...</summaryText> 标签内。]\n{{else if (get_chat_variable 'infobar_upm_character_data_v1.summary.needsSmallSummary')}}\n[角色扮演提示：现在你需要进行一次“小剧情总结”。请回顾最近 {{get_chat_variable 'infobar_upm_character_data_v1.summary.smallSummaryInterval' '10'}} 条左右的AI回复内容（当前AI将要回复的这一轮对话前，其自身计数为 {{get_chat_variable 'infobar_upm_character_data_v1.summary.summaryTriggerMsgCount' '未知'}}），用100-200字简要概括期间发生的主要事件和互动。将总结内容放在 <summaryText type=\"small\">...</summaryText> 标签内。]\n{{/if}}\n\n--- 指令结束 ---\n请严格遵循以上所有指令。自我反思环节和数据持久性原则至关重要。\n]\n", "constant": true, "vectorized": false, "selective": true, "selectiveLogic": 0, "addMemo": true, "order": 100, "position": 4, "disable": false, "excludeRecursion": false, "preventRecursion": false, "delayUntilRecursion": false, "probability": 100, "useProbability": true, "depth": 0, "group": "", "groupOverride": false, "groupWeight": 100, "scanDepth": null, "caseSensitive": null, "matchWholeWords": null, "useGroupScoring": null, "automationId": "", "role": 0, "sticky": 0, "cooldown": 0, "delay": 0, "displayIndex": 2}}}
# SillyTavern世界书系统技术实现文档

## 📋 目录
1. [系统概述](#系统概述)
2. [核心架构](#核心架构)
3. [数据结构设计](#数据结构设计)
4. [关键算法实现](#关键算法实现)
5. [性能优化策略](#性能优化策略)
6. [API接口设计](#api接口设计)
7. [实现示例代码](#实现示例代码)
8. [最佳实践建议](#最佳实践建议)

## 系统概述

### 设计目标
- **动态知识注入**：根据对话内容自动注入相关背景信息
- **Token效率**：避免长期占用上下文，只在需要时注入
- **精确控制**：创作者可精确控制信息的触发条件和时机
- **可扩展性**：支持复杂的逻辑组合和状态管理

### 核心特性
- 多层关键词匹配系统
- 递归触发机制
- 时间效果管理（粘性、冷却、延迟）
- 分组竞争和优先级控制
- 动态深度扫描
- Token预算管理

## 核心架构

### 系统组件图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户输入      │───▶│  世界书引擎     │───▶│   AI模型        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  知识库存储     │
                    └─────────────────┘
```

### 核心类设计

#### 1. WorldInfoEngine（世界书引擎）
```typescript
class WorldInfoEngine {
    private buffer: WorldInfoBuffer;
    private timedEffects: WorldInfoTimedEffects;
    private entries: WorldInfoEntry[];
    private config: WorldInfoConfig;
    
    async processChat(messages: string[]): Promise<WorldInfoResult>;
    private scanEntries(scanState: ScanState): Set<WorldInfoEntry>;
    private filterByLogic(entries: WorldInfoEntry[]): WorldInfoEntry[];
    private applyTimedEffects(entries: WorldInfoEntry[]): WorldInfoEntry[];
}
```

#### 2. WorldInfoBuffer（扫描缓冲区）
```typescript
class WorldInfoBuffer {
    private depthBuffer: string[];      // 按深度排序的消息
    private recurseBuffer: string[];    // 递归内容缓冲
    private injectBuffer: string[];     // 注入内容缓冲
    private globalScanData: GlobalScanData;
    private skew: number;               // 深度偏移
    
    get(entry: WorldInfoEntry, scanState: ScanState): string;
    matchKeys(haystack: string, needle: string, entry: WorldInfoEntry): boolean;
    addRecurse(content: string): void;
    getScore(entry: WorldInfoEntry, scanState: ScanState): number;
}
```

#### 3. WorldInfoTimedEffects（时间效果管理）
```typescript
class WorldInfoTimedEffects {
    private stickyEntries: Map<string, TimedEffect>;
    private cooldownEntries: Map<string, TimedEffect>;
    private delayEntries: Map<string, TimedEffect>;
    
    isEffectActive(type: EffectType, entry: WorldInfoEntry): boolean;
    setTimedEffect(type: EffectType, entry: WorldInfoEntry): void;
    cleanExpiredEffects(): void;
}
```

## 数据结构设计

### 世界书条目结构
```typescript
interface WorldInfoEntry {
    uid: number;                    // 唯一标识符
    key: string[];                  // 主关键词数组
    keysecondary: string[];         // 次关键词数组
    content: string;                // 条目内容
    comment: string;                // 条目注释/标题
    
    // 激活控制
    selective: boolean;             // 是否使用选择性激活
    selectiveLogic: LogicType;      // 逻辑类型（AND_ANY, AND_ALL, NOT_ANY, NOT_ALL）
    constant: boolean;              // 是否常驻激活
    disable: boolean;               // 是否禁用
    
    // 概率和权重
    probability: number;            // 激活概率 (0-100)
    useProbability: boolean;        // 是否使用概率
    order: number;                  // 排序权重
    
    // 位置控制
    position: PositionType;         // 插入位置
    depth: number;                  // 插入深度
    role: RoleType;                 // 角色类型
    
    // 递归控制
    preventRecursion: boolean;      // 阻止递归
    excludeRecursion: boolean;      // 排除递归扫描
    delayUntilRecursion: number;    // 延迟到递归级别
    
    // 匹配选项
    caseSensitive?: boolean;        // 大小写敏感
    matchWholeWords?: boolean;      // 全词匹配
    scanDepth?: number;             // 扫描深度
    
    // 分组和过滤
    group: string;                  // 分组名称
    groupWeight: number;            // 组内权重
    useGroupScoring: boolean;       // 使用组评分
    
    // 时间效果
    sticky?: number;                // 粘性持续时间
    cooldown?: number;              // 冷却时间
    delay?: number;                 // 延迟时间
    
    // 匹配范围
    matchPersonaDescription: boolean;
    matchCharacterDescription: boolean;
    matchCharacterPersonality: boolean;
    matchScenario: boolean;
    matchCreatorNotes: boolean;
}
```

### 配置结构
```typescript
interface WorldInfoConfig {
    depth: number;                  // 默认扫描深度
    budget: number;                 // Token预算百分比
    budgetCap: number;              // Token预算上限
    minActivations: number;         // 最小激活数量
    maxRecursionSteps: number;      // 最大递归步数
    recursive: boolean;             // 启用递归
    caseSensitive: boolean;         // 全局大小写敏感
    matchWholeWords: boolean;       // 全局全词匹配
    useGroupScoring: boolean;       // 全局组评分
    overflowAlert: boolean;         // 预算溢出警告
}
```

### 扫描状态枚举
```typescript
enum ScanState {
    NONE = 0,           // 停止扫描
    INITIAL = 1,        // 初始扫描
    RECURSION = 2,      // 递归扫描
    MIN_ACTIVATIONS = 3 // 最小激活扫描
}

enum LogicType {
    AND_ANY = 0,        // 主关键词 + 任一次关键词
    NOT_ALL = 1,        // 主关键词 + 非全部次关键词
    NOT_ANY = 2,        // 主关键词 + 无任何次关键词
    AND_ALL = 3         // 主关键词 + 全部次关键词
}

enum PositionType {
    BEFORE = 0,         // 角色描述前
    AFTER = 1,          // 角色描述后
    AT_DEPTH = 6        // 指定深度
}
```

## 关键算法实现

### 1. 核心扫描算法
```typescript
async function scanWorldInfo(
    messages: string[], 
    globalData: GlobalScanData,
    config: WorldInfoConfig
): Promise<WorldInfoResult> {
    const buffer = new WorldInfoBuffer(messages, globalData);
    const timedEffects = new WorldInfoTimedEffects();
    const allActivatedEntries = new Map<string, WorldInfoEntry>();
    
    let scanState = ScanState.INITIAL;
    let recursionCount = 0;
    let budget = calculateBudget(config);
    
    while (scanState !== ScanState.NONE) {
        // 防止无限递归
        if (config.maxRecursionSteps && recursionCount >= config.maxRecursionSteps) {
            break;
        }
        
        const activatedNow = new Set<WorldInfoEntry>();
        
        // 扫描所有条目
        for (const entry of sortedEntries) {
            if (shouldSkipEntry(entry, allActivatedEntries, timedEffects, scanState)) {
                continue;
            }
            
            if (await checkEntryActivation(entry, buffer, scanState)) {
                activatedNow.add(entry);
            }
        }
        
        // 应用过滤器
        const filteredEntries = applyFilters(activatedNow, buffer, scanState, timedEffects);
        
        // 检查预算
        const newContent = filteredEntries.map(e => e.content).join('\n');
        if (await exceedsBudget(newContent, budget)) {
            break;
        }
        
        // 添加到激活列表
        filteredEntries.forEach(entry => {
            allActivatedEntries.set(`${entry.world}.${entry.uid}`, entry);
        });
        
        // 决定下一步扫描状态
        scanState = determineNextScanState(
            filteredEntries, 
            allActivatedEntries, 
            buffer, 
            config
        );
        
        // 添加递归内容
        if (scanState === ScanState.RECURSION) {
            const recurseContent = filteredEntries
                .filter(e => !e.preventRecursion)
                .map(e => e.content)
                .join('\n');
            buffer.addRecurse(recurseContent);
        }
        
        recursionCount++;
    }
    
    return buildResult(allActivatedEntries);
}
```

### 2. 关键词匹配算法
```typescript
function matchKeys(
    haystack: string, 
    needle: string, 
    entry: WorldInfoEntry
): boolean {
    // 正则表达式匹配
    const regexMatch = parseRegexFromString(needle);
    if (regexMatch) {
        return regexMatch.test(haystack);
    }
    
    // 大小写处理
    const caseSensitive = entry.caseSensitive ?? globalConfig.caseSensitive;
    const searchText = caseSensitive ? haystack : haystack.toLowerCase();
    const searchKey = caseSensitive ? needle : needle.toLowerCase();
    
    // 全词匹配
    const matchWholeWords = entry.matchWholeWords ?? globalConfig.matchWholeWords;
    if (matchWholeWords) {
        const words = searchKey.split(/\s+/);
        if (words.length > 1) {
            return searchText.includes(searchKey);
        } else {
            const regex = new RegExp(`(?:^|\\W)(${escapeRegex(searchKey)})(?:$|\\W)`);
            return regex.test(searchText);
        }
    }
    
    return searchText.includes(searchKey);
}
```

### 3. 逻辑组合算法
```typescript
function checkSecondaryKeywords(
    entry: WorldInfoEntry,
    textToScan: string
): boolean {
    if (!entry.keysecondary?.length) {
        return true; // 无次关键词，直接通过
    }

    let hasAnyMatch = false;
    let hasAllMatch = true;

    for (const secondaryKey of entry.keysecondary) {
        const hasMatch = matchKeys(textToScan, secondaryKey, entry);

        if (hasMatch) hasAnyMatch = true;
        if (!hasMatch) hasAllMatch = false;

        // 早期退出优化
        switch (entry.selectiveLogic) {
            case LogicType.AND_ANY:
                if (hasMatch) return true;
                break;
            case LogicType.NOT_ALL:
                if (!hasMatch) return true;
                break;
        }
    }

    switch (entry.selectiveLogic) {
        case LogicType.AND_ANY:
            return hasAnyMatch;
        case LogicType.AND_ALL:
            return hasAllMatch;
        case LogicType.NOT_ANY:
            return !hasAnyMatch;
        case LogicType.NOT_ALL:
            return !hasAllMatch;
        default:
            return false;
    }
}
```

### 4. 分组竞争算法
```typescript
function filterByInclusionGroups(
    entries: WorldInfoEntry[],
    buffer: WorldInfoBuffer,
    scanState: ScanState
): WorldInfoEntry[] {
    // 按组分类
    const groups = new Map<string, WorldInfoEntry[]>();

    entries.forEach(entry => {
        if (!entry.group) return;

        entry.group.split(/,\s*/).forEach(groupName => {
            if (!groups.has(groupName)) {
                groups.set(groupName, []);
            }
            groups.get(groupName)!.push(entry);
        });
    });

    const toRemove = new Set<WorldInfoEntry>();

    // 处理每个组
    groups.forEach((groupEntries, groupName) => {
        // 计算每个条目的得分
        const scores = groupEntries.map(entry => ({
            entry,
            score: buffer.getScore(entry, scanState)
        }));

        // 找到最高分
        const maxScore = Math.max(...scores.map(s => s.score));

        // 移除低分条目
        scores.forEach(({ entry, score }) => {
            if (score < maxScore && entry.useGroupScoring) {
                toRemove.add(entry);
            }
        });
    });

    return entries.filter(entry => !toRemove.has(entry));
}
```

### 5. 时间效果管理算法
```typescript
class WorldInfoTimedEffects {
    private effects = new Map<string, Map<EffectType, TimedEffect>>();

    isEffectActive(type: EffectType, entry: WorldInfoEntry): boolean {
        const key = `${entry.world}.${entry.uid}`;
        const entryEffects = this.effects.get(key);

        if (!entryEffects?.has(type)) {
            return false;
        }

        const effect = entryEffects.get(type)!;
        const currentChatIndex = this.getCurrentChatIndex();

        return currentChatIndex >= effect.start &&
               currentChatIndex <= effect.end;
    }

    setTimedEffect(
        type: EffectType,
        entry: WorldInfoEntry,
        duration: number
    ): void {
        const key = `${entry.world}.${entry.uid}`;
        const currentChatIndex = this.getCurrentChatIndex();

        if (!this.effects.has(key)) {
            this.effects.set(key, new Map());
        }

        this.effects.get(key)!.set(type, {
            start: currentChatIndex,
            end: currentChatIndex + duration,
            protected: false
        });
    }

    cleanExpiredEffects(): void {
        const currentChatIndex = this.getCurrentChatIndex();

        this.effects.forEach((entryEffects, key) => {
            entryEffects.forEach((effect, type) => {
                if (currentChatIndex > effect.end && !effect.protected) {
                    entryEffects.delete(type);
                }
            });

            if (entryEffects.size === 0) {
                this.effects.delete(key);
            }
        });
    }
}
```

## 性能优化策略

### 1. 缓存机制
```typescript
class WorldInfoCache {
    private entryCache = new Map<string, WorldInfoEntry[]>();
    private contentCache = new Map<string, string>();
    private scoreCache = new Map<string, number>();

    getEntries(worldName: string): WorldInfoEntry[] | null {
        return this.entryCache.get(worldName) || null;
    }

    setEntries(worldName: string, entries: WorldInfoEntry[]): void {
        this.entryCache.set(worldName, entries);
    }

    invalidateWorld(worldName: string): void {
        this.entryCache.delete(worldName);
        // 清理相关的内容和得分缓存
        for (const key of this.contentCache.keys()) {
            if (key.startsWith(worldName)) {
                this.contentCache.delete(key);
            }
        }
    }
}
```

### 2. 防抖保存
```typescript
class WorldInfoStorage {
    private saveQueue = new Map<string, WorldInfoData>();
    private saveTimer: NodeJS.Timeout | null = null;

    scheduleSave(worldName: string, data: WorldInfoData): void {
        this.saveQueue.set(worldName, data);

        if (this.saveTimer) {
            clearTimeout(this.saveTimer);
        }

        this.saveTimer = setTimeout(() => {
            this.flushSaveQueue();
        }, 1000); // 1秒防抖
    }

    private async flushSaveQueue(): Promise<void> {
        const saves = Array.from(this.saveQueue.entries());
        this.saveQueue.clear();

        await Promise.all(saves.map(([name, data]) =>
            this.saveWorldInfo(name, data)
        ));
    }
}
```

### 3. 增量更新
```typescript
class WorldInfoDelta {
    private originalData: Map<string, any> = new Map();

    trackChanges(entry: WorldInfoEntry): void {
        const key = `${entry.world}.${entry.uid}`;
        if (!this.originalData.has(key)) {
            this.originalData.set(key, { ...entry });
        }
    }

    getChanges(entry: WorldInfoEntry): Partial<WorldInfoEntry> {
        const key = `${entry.world}.${entry.uid}`;
        const original = this.originalData.get(key);

        if (!original) return entry;

        const changes: Partial<WorldInfoEntry> = {};
        for (const [field, value] of Object.entries(entry)) {
            if (original[field] !== value) {
                changes[field] = value;
            }
        }

        return changes;
    }
}
```

## API接口设计

### 1. 核心API
```typescript
interface WorldInfoAPI {
    // 世界书管理
    createWorld(name: string): Promise<void>;
    deleteWorld(name: string): Promise<void>;
    renameWorld(oldName: string, newName: string): Promise<void>;
    listWorlds(): Promise<string[]>;

    // 条目管理
    createEntry(worldName: string, entry: Partial<WorldInfoEntry>): Promise<number>;
    updateEntry(worldName: string, uid: number, changes: Partial<WorldInfoEntry>): Promise<void>;
    deleteEntry(worldName: string, uid: number): Promise<void>;
    getEntry(worldName: string, uid: number): Promise<WorldInfoEntry | null>;
    listEntries(worldName: string): Promise<WorldInfoEntry[]>;

    // 搜索和过滤
    searchEntries(worldName: string, query: string): Promise<WorldInfoEntry[]>;
    filterEntries(worldName: string, filter: EntryFilter): Promise<WorldInfoEntry[]>;

    // 处理和激活
    processChat(messages: string[], config?: WorldInfoConfig): Promise<WorldInfoResult>;
    dryRun(messages: string[], config?: WorldInfoConfig): Promise<WorldInfoEntry[]>;

    // 导入导出
    exportWorld(worldName: string): Promise<WorldInfoData>;
    importWorld(data: WorldInfoData): Promise<void>;
}
```

### 2. 事件系统
```typescript
interface WorldInfoEvents {
    'entry-activated': (entry: WorldInfoEntry) => void;
    'entry-created': (worldName: string, entry: WorldInfoEntry) => void;
    'entry-updated': (worldName: string, uid: number, changes: Partial<WorldInfoEntry>) => void;
    'entry-deleted': (worldName: string, uid: number) => void;
    'world-created': (worldName: string) => void;
    'world-deleted': (worldName: string) => void;
    'budget-exceeded': (usedTokens: number, budget: number) => void;
}

class WorldInfoEventEmitter extends EventEmitter {
    onEntryActivated(callback: (entry: WorldInfoEntry) => void): void {
        this.on('entry-activated', callback);
    }

    onBudgetExceeded(callback: (used: number, budget: number) => void): void {
        this.on('budget-exceeded', callback);
    }
}
```

## 实现示例代码

### 1. 简化版世界书引擎
```typescript
class SimpleWorldInfoEngine {
    private entries: WorldInfoEntry[] = [];
    private config: WorldInfoConfig;

    constructor(config: WorldInfoConfig) {
        this.config = config;
    }

    async process(messages: string[]): Promise<string[]> {
        const activatedEntries: WorldInfoEntry[] = [];
        const combinedText = messages.join('\n').toLowerCase();

        // 第一轮：主关键词匹配
        for (const entry of this.entries) {
            if (entry.disable) continue;

            const hasMainKeyword = entry.key.some(keyword =>
                combinedText.includes(keyword.toLowerCase())
            );

            if (!hasMainKeyword) continue;

            // 检查次关键词逻辑
            if (this.checkSecondaryLogic(entry, combinedText)) {
                activatedEntries.push(entry);
            }
        }

        // 应用概率过滤
        const probabilityFiltered = activatedEntries.filter(entry => {
            if (!entry.useProbability) return true;
            return Math.random() * 100 <= entry.probability;
        });

        // 按优先级排序
        probabilityFiltered.sort((a, b) => b.order - a.order);

        return probabilityFiltered.map(entry => entry.content);
    }

    private checkSecondaryLogic(entry: WorldInfoEntry, text: string): boolean {
        if (!entry.keysecondary?.length) return true;

        const matches = entry.keysecondary.map(keyword =>
            text.includes(keyword.toLowerCase())
        );

        switch (entry.selectiveLogic) {
            case LogicType.AND_ANY:
                return matches.some(m => m);
            case LogicType.AND_ALL:
                return matches.every(m => m);
            case LogicType.NOT_ANY:
                return !matches.some(m => m);
            case LogicType.NOT_ALL:
                return !matches.every(m => m);
            default:
                return true;
        }
    }
}
```

### 2. 使用示例
```typescript
// 创建世界书引擎
const engine = new SimpleWorldInfoEngine({
    depth: 4,
    budget: 25,
    budgetCap: 1000,
    recursive: true,
    caseSensitive: false,
    matchWholeWords: false
});

// 添加条目
engine.addEntry({
    uid: 1,
    key: ['北京大学', '北大'],
    keysecondary: [],
    content: '北京大学是中国顶尖学府，位于海淀区，有美丽的未名湖。',
    selective: true,
    selectiveLogic: LogicType.AND_ANY,
    probability: 100,
    order: 100,
    position: PositionType.AFTER
});

// 处理对话
const messages = [
    '用户：我在北京大学上学',
    'AI：那很棒啊！',
    '用户：你知道北大有什么特色吗？'
];

const result = await engine.process(messages);
console.log('激活的世界书内容：', result);
```

## 最佳实践建议

### 1. 条目设计原则
```typescript
// ✅ 好的条目设计
const goodEntry: WorldInfoEntry = {
    key: ['星巴克', '咖啡店', 'Starbucks'],  // 多个相关关键词
    keysecondary: ['咖啡', '拿铁'],          // 相关的次关键词
    content: '星巴克是全球知名的咖啡连锁店，以拿铁和卡布奇诺闻名。',
    probability: 80,                        // 适度的随机性
    order: 100                             // 合理的优先级
};

// ❌ 避免的设计
const badEntry: WorldInfoEntry = {
    key: ['星巴克咖啡店拿铁卡布奇诺'],      // 关键词过长
    content: '星巴克...(3000字的详细介绍)', // 内容过长
    probability: 100,                       // 总是触发
    order: 999                             // 过高优先级
};
```

### 2. 性能优化建议
```typescript
// 条目数量控制
const RECOMMENDED_LIMITS = {
    maxEntriesPerWorld: 1000,      // 单个世界书最大条目数
    maxActiveEntries: 50,          // 单次最大激活条目数
    maxContentLength: 500,         // 单个条目最大长度
    maxKeywords: 10                // 单个条目最大关键词数
};

// 缓存策略
class PerformanceOptimizer {
    // 预编译正则表达式
    private regexCache = new Map<string, RegExp>();

    getRegex(pattern: string): RegExp {
        if (!this.regexCache.has(pattern)) {
            this.regexCache.set(pattern, new RegExp(pattern, 'i'));
        }
        return this.regexCache.get(pattern)!;
    }

    // 批量处理
    processBatch(entries: WorldInfoEntry[], batchSize = 100): Promise<void> {
        const batches = this.chunkArray(entries, batchSize);
        return Promise.all(batches.map(batch => this.processBatchInternal(batch)));
    }
}
```

### 3. 错误处理和日志
```typescript
class WorldInfoLogger {
    private debugMode: boolean;

    logActivation(entry: WorldInfoEntry, reason: string): void {
        if (this.debugMode) {
            console.log(`[WI] Entry ${entry.uid} activated: ${reason}`, {
                keywords: entry.key,
                content: entry.content.substring(0, 100) + '...'
            });
        }
    }

    logSkip(entry: WorldInfoEntry, reason: string): void {
        if (this.debugMode) {
            console.log(`[WI] Entry ${entry.uid} skipped: ${reason}`);
        }
    }

    logError(error: Error, context: any): void {
        console.error('[WI] Error:', error.message, context);
    }
}

// 错误恢复机制
class WorldInfoErrorHandler {
    handleEntryError(entry: WorldInfoEntry, error: Error): void {
        // 记录错误但不中断整个流程
        console.warn(`Entry ${entry.uid} failed:`, error.message);

        // 可选：标记问题条目
        entry.disable = true;
    }

    validateEntry(entry: WorldInfoEntry): string[] {
        const errors: string[] = [];

        if (!entry.key?.length) {
            errors.push('Entry must have at least one keyword');
        }

        if (!entry.content?.trim()) {
            errors.push('Entry content cannot be empty');
        }

        if (entry.probability < 0 || entry.probability > 100) {
            errors.push('Probability must be between 0 and 100');
        }

        return errors;
    }
}
```

### 4. 测试策略
```typescript
// 单元测试示例
describe('WorldInfoEngine', () => {
    let engine: WorldInfoEngine;

    beforeEach(() => {
        engine = new WorldInfoEngine(defaultConfig);
    });

    test('should activate entry with matching keyword', async () => {
        const entry = createTestEntry({
            key: ['test'],
            content: 'Test content'
        });

        engine.addEntry(entry);

        const result = await engine.process(['This is a test message']);
        expect(result.activatedEntries).toContain(entry);
    });

    test('should respect probability settings', async () => {
        const entry = createTestEntry({
            key: ['test'],
            probability: 0 // 0% 概率
        });

        engine.addEntry(entry);

        const result = await engine.process(['test']);
        expect(result.activatedEntries).not.toContain(entry);
    });

    test('should handle recursive activation', async () => {
        const entry1 = createTestEntry({
            key: ['trigger'],
            content: 'This mentions recursive'
        });

        const entry2 = createTestEntry({
            key: ['recursive'],
            content: 'Recursive content'
        });

        engine.addEntry(entry1);
        engine.addEntry(entry2);

        const result = await engine.process(['trigger word']);
        expect(result.activatedEntries).toHaveLength(2);
    });

    test('should handle group competition', async () => {
        const entry1 = createTestEntry({
            key: ['test'],
            group: 'testGroup',
            order: 100
        });

        const entry2 = createTestEntry({
            key: ['test'],
            group: 'testGroup',
            order: 200 // 更高优先级
        });

        engine.addEntry(entry1);
        engine.addEntry(entry2);

        const result = await engine.process(['test']);
        expect(result.activatedEntries).toContain(entry2);
        expect(result.activatedEntries).not.toContain(entry1);
    });
});

// 集成测试示例
describe('WorldInfo Integration', () => {
    test('should handle complex scenario', async () => {
        const worldData = loadTestWorld('complex-fantasy-world');
        const engine = new WorldInfoEngine(defaultConfig);

        await engine.loadWorld(worldData);

        const conversation = [
            '用户：我来到了魔法学院',
            'AI：欢迎来到霍格沃茨！',
            '用户：我想学习火系魔法',
            'AI：火系魔法需要很强的意志力...'
        ];

        const result = await engine.process(conversation);

        // 验证激活了正确的条目
        expect(result.activatedEntries.some(e => e.key.includes('魔法学院'))).toBe(true);
        expect(result.activatedEntries.some(e => e.key.includes('火系魔法'))).toBe(true);

        // 验证没有激活无关条目
        expect(result.activatedEntries.some(e => e.key.includes('水系魔法'))).toBe(false);
    });
});
```

## 总结

SillyTavern的世界书系统是一个精心设计的知识管理系统，它通过以下核心特性解决了AI对话中的上下文管理问题：

### 🎯 核心优势
1. **精确控制** - 创作者可以精确控制何时注入什么信息
2. **Token效率** - 只在需要时注入相关信息，避免浪费上下文
3. **复杂逻辑** - 支持AND/OR/NOT等复杂的触发条件
4. **状态管理** - 通过时间效果实现复杂的状态持久化
5. **可扩展性** - 模块化设计便于扩展和维护

### 🚀 实现建议
1. **从简单开始** - 先实现基础的关键词匹配功能
2. **逐步增强** - 然后添加逻辑组合、递归、时间效果等高级功能
3. **性能优化** - 在功能完善后重点关注缓存和性能优化
4. **用户体验** - 提供直观的编辑界面和调试工具

### 📚 扩展方向
- **AI辅助编写** - 使用AI帮助生成和优化世界书条目
- **可视化编辑** - 提供图形化的条目关系编辑器
- **版本控制** - 支持世界书的版本管理和协作编辑
- **模板系统** - 提供常用场景的世界书模板

这份文档涵盖了SillyTavern世界书系统的核心实现细节，你可以根据自己的需求选择性地实现这些功能。建议从简化版本开始，逐步添加高级特性。

---

*文档版本：1.0*
*最后更新：2025年*
*基于SillyTavern项目分析整理*
```
```
```
```
```
